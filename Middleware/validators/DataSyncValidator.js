const { query, body } = require('express-validator');

const {
    headerValidator,
    tenantIdQueryValidator,

    lastSyncedAtQueryValidator,
    lastSyncedAtBodyValidator,

    isInitialSyncQueryValidator,
    isInitialSyncBodyValidator,

    perPageValidator,
    perPageBodyValidator,

    cursorStringIdQueryValidator,
    cursorMongoIdBodyValidator,
    cursorMongoIdQueryValidator,

    updateUserRoleSettingsBodyValidator,
} = require('./CommonValidator');

const { SETTINGS, PRIMITIVE_ROLES } = require('../../Configs/constants');

exports.getTenantAppSetting = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("type", "Please provide valid 'type'")
        .trim()
        .notEmpty()
        .equals(SETTINGS.CONFIGURATIONS.TYPE.APP_SETTING)
        .withMessage(`'type' value must be ${SETTINGS.CONFIGURATIONS.TYPE.APP_SETTING}`),
];

exports.getCommonDataValidator = [
    ...headerValidator,
    ...lastSyncedAtQueryValidator,
    ...isInitialSyncQueryValidator,
    ...perPageValidator,
];

exports.getUserRoleSettings = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...lastSyncedAtQueryValidator,
    ...cursorStringIdQueryValidator,
    ...perPageValidator,
];

exports.updateUserRoleSettings = [
    ...headerValidator,
    ...updateUserRoleSettingsBodyValidator,
];

exports.getUserRoles = [
    ...this.getCommonDataValidator,
    ...tenantIdQueryValidator,
    ...cursorMongoIdQueryValidator,

    query("roles", "Please provide valid 'roles'(i.e.min 1) in array")
        .isArray({ min: 1 }),

    query("roles.*", "Please provide valid 'roles'")
        .isIn(Object.values(PRIMITIVE_ROLES))
        .withMessage(`'roles' value must be one of "${Object.values(PRIMITIVE_ROLES).join(", ")}"`),
];

exports.getRegions = [
    ...this.getCommonDataValidator,
    ...cursorMongoIdQueryValidator,

    query("countryId", "Please provide valid 'countryId'")
        .isMongoId()
        .withMessage("Please provide valid 'countryId' as MongoDB ObjectId"),
];

exports.getCities = [
    ...headerValidator,
    ...lastSyncedAtBodyValidator,
    ...isInitialSyncBodyValidator,
    ...cursorMongoIdBodyValidator,
    ...perPageBodyValidator,

    body("regionIds", "Please provide valid 'regionIds'(i.e.min 1) in array")
        .isArray({ min: 1 }),

    body("regionIds.*", "Please provide valid 'regionIds'")
        .isMongoId()
        .withMessage("Please provide valid 'regionIds' as MongoDB ObjectId"),
];
