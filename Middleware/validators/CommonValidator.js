const { header, body, query, buildCheckFunction } = require("express-validator")

const { ENTITY_STATUS } = require("../../Configs/constants")
const checkBodyAndQuery = buildCheckFunction(["body", "query"])

exports.headerValidator = [
    header("authorization", "Please provide 'authorization'")
        .trim()
        .notEmpty(),

    header("devicetoken", "Please provide 'devicetoken'")
        .trim()
        .notEmpty(),

    header("userroleid", "Please provide 'userroleid'")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid 'userroleid'")
]

exports.tenantIdValidator = [
    checkBodyAndQuery("tenantId")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please provide valid 'tenantId'"),
]

exports.tenantIdBodyValidator = [
    body("tenantId")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please provide valid 'tenantId'"),
]

exports.updateUserRoleSettingsBodyValidator = [
    body("tenant_id")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please provide valid 'tenant_id'"),
    body("user_role_id")
        .isMongoId()
        .withMessage("Please provide valid 'user_role_id' as MongoDB ObjectId"),
    body("updated_at")
        .trim()
        .notEmpty()
        .bail()
        .custom((value) => {
            const date = moment(value);
            return date.isValid();
        })
        .withMessage("Please provide valid date format for 'updated_at'"),
]


exports.tenantIdQueryValidator = [
    query("tenantId")
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please provide valid 'tenantId'"),
]

exports.updatedAtBodyValidator = [
    body("updatedAt")
        .trim()
        .notEmpty()
        .bail()
        .custom((value) => {
            const date = moment(value);
            return date.isValid();
        })
        .withMessage("Please provide valid date format for 'updatedAt'"),
]



exports.isActiveValidator = [
    body("isActive", "Please send isActive value either true or false.")
        .isBoolean()
]

exports.statusValidator = [
    query("status")
        .trim()
        .default(ENTITY_STATUS.ALL)
        .isIn(Object.values(ENTITY_STATUS))
        .withMessage(`'status' must be ${Object.values(ENTITY_STATUS).join(" or ")}`),
]

exports.statusBodyValidator = [
    body("status")
        .trim()
        .default(ENTITY_STATUS.ALL)
        .isIn(Object.values(ENTITY_STATUS))
        .withMessage(`'status' must be ${Object.values(ENTITY_STATUS).join(" or ")}`),
]

exports.searchKeyValidator = [
    query("searchKey")
        .trim()
        .custom((value) => {
            if (value && value.toString().length < 3) {
                return false;
            }
            return true;
        })
        .withMessage("Minimum search value must be 3 characters long."),
];

exports.pageValidator = [
    query("page", "Please provide page.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid page."),
]

exports.perPageValidator = [
    query("perPage", "Please provide per page limit.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid per page limit."),
]

exports.pageBodyValidator = [
    body("page", "Please provide page.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid page."),
]

exports.perPageBodyValidator = [
    body("perPage", "Please provide per page limit.")
        .toInt()
        .isInt({ min: 1 })
        .withMessage("Please provide valid per page limit."),
]

exports.getAttributeValidator = [
    ...this.headerValidator,
    ...this.tenantIdValidator,
    ...this.statusValidator,
    ...this.searchKeyValidator,
    ...this.pageValidator,
    ...this.perPageValidator,
]

exports.idsValidator = [
    body("ids", "Please provide valid ids in array.")
        .isArray({ min: 1 })
]

exports.deleteAttributesValidator = [
    ...this.headerValidator,
    ...this.tenantIdValidator,
    ...this.idsValidator,
]

exports.timezoneQueryValidator = [
    query("timezone", "Please provide valid timezone")
        .trim()
        .notEmpty(),
]

exports.timezoneBodyValidator = [
    body("timezone", "Please provide valid timezone")
        .trim()
        .notEmpty(),
]

exports.userRoleIdBodyValidator = [
    body("userRoleId", "Please provide userRoleId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
]

exports.userRoleIdQueryValidator = [
    query("userRoleId", "Please provide userRoleId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
]

exports.nameBodyValidator = [
    body("name", "Please provide name")
        .trim()
        .notEmpty(),
]

exports.projectionQueryValidator = [
    query("projections", "Please provide projections")
        .default([])
        .isArray(),

    query("projections.*", "Please provide valid projections")
        .trim()
        .notEmpty()
]

exports.secondaryLanguageNameBodyValidator = [
    body("secondaryLanguageName", "Please provide secondaryLanguageName")
        .trim()
        .notEmpty(),
]

exports.customerExternalIdQueryValidator = [
    query("customerExternalId", "Please provide valid customerExternalId")
        .trim()
        .notEmpty()
]

exports.customerExternalIdBodyValidator = [
    body("customerExternalId", "Please provide valid customerExternalId")
        .trim()
        .notEmpty()
]

exports.customerUserRoleIdBodyValidator = [
    body("customerUserRoleId", "Please provide customerUserRoleId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid customerUserRoleId"),
]

exports.customerUserRoleIdQueryValidator = [
    query("customerUserRoleId", "Please provide customerUserRoleId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid customerUserRoleId"),
]

exports.lastSyncedAtQueryValidator = [
    query("lastSyncedAt", "Please provide valid 'lastSyncedAt' date")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .custom((value) => {
            const date = moment(value);
            return date.isValid();
        })
        .withMessage("Please provide valid date format for 'lastSyncedAt'"),
]

exports.isInitialSyncQueryValidator = [
    query("isInitialSync", "Please provide valid 'isInitialSync'")
        .optional()
        .isBoolean()
        .withMessage("'isInitialSync' must be a boolean value"),
]

exports.cursorStringIdQueryValidator = [
    query("cursor", "Please provide valid 'cursor'")
        .optional()
        .trim()
        .notEmpty()
        .bail()
]

exports.cursorMongoIdQueryValidator = [
    query("cursor", "Please provide valid 'cursor'")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid 'cursor' as MongoDB ObjectId"),
]

exports.lastSyncedAtBodyValidator = [
    body("lastSyncedAt", "Please provide valid 'lastSyncedAt' date")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .custom((value) => {
            const date = moment(value);
            return date.isValid();
        })
        .withMessage("Please provide valid date format for 'lastSyncedAt'"),
]

exports.isInitialSyncBodyValidator = [
    body("isInitialSync", "Please provide valid 'isInitialSync'")
        .optional()
        .isBoolean()
        .withMessage("'isInitialSync' must be a boolean value"),
]

exports.cursorMongoIdBodyValidator = [
    body("cursor", "Please provide valid 'cursor'")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid 'cursor' as MongoDB ObjectId"),
]
