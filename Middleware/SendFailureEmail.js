const { sendEmail } = require("../Configs/mailer")

const { EMAIL_ID, VALUES } = require("../Configs/constants")

exports.default = async (req, errors) => {
    try {
        const {
            originalUrl,
            method,
            headers: {
                userroleid,
                userDetails: {
                    _id,
                    first_name,
                    last_name,
                    email,
                    mobile_number,
                    country_code,
                    is_active,
                    is_deleted,
                } = {},
                devicetoken,
                devicetype,
                portalType,
                deviceTypeDetected,
                deviceaccesstype,
                version,
                build,
            } = {},
            body: {
                tenantId,
                customerName,
            },
        } = req

        if (
            !VALUES.IS_DEV_ENV &&
            (
                (
                    ["/tenant-portal/customer"].includes(originalUrl) &&
                    method === "PUT"
                ) ||
                (
                    ["/tenant-portal/customers"].includes(originalUrl) &&
                    ["PUT", "POST"].includes(method)
                )
            )
        ) {
            const reason =
                typeof errors === "object" && !Array.isArray(errors)
                    ? (`
                    <pre>
                        ${JSON.stringify(errors, undefined, 4)}
                    </pre>
                    `
                    )
                    : errors

            const payload = {
                "from": EMAIL_ID.NOTIFICATION,
                // "to": EMAIL_ID.FORAM,
                "to": EMAIL_ID.FARIS,
                "cc": [
                    EMAIL_ID.YOUSEF,
                    EMAIL_ID.YASHASHWINI,
                    EMAIL_ID.AALAP,
                    EMAIL_ID.FORAM,
                    EMAIL_ID.RAJAN,
                ],
                "subject": `Failed To Update Customer`,
                "data": {
                    "html": `
                        Failed to update customer${customerName ? ` - ${customerName}` : ""}.
                        <br/><br/>

                        <b> Environment: </b> ${VALUES.ENVIRONMENT}
                        <br/><br/>

                        <b> Tenant Id: </b> ${tenantId || "N/A"}
                        <br/><br/>

                        <b> API: </b> ${method} ${originalUrl}
                        <br/><br/>

                        <b> API Request Headers: </b>
                        <pre> ${JSON.stringify(
                            {
                                userroleid,
                                userDetails: {
                                    _id,
                                    first_name,
                                    last_name,
                                    email,
                                    mobile_number,
                                    country_code,
                                    is_active,
                                    is_deleted,
                                },
                                devicetoken,
                                devicetype,
                                portalType,
                                deviceTypeDetected,
                                deviceaccesstype,
                                version,
                                build,
                            },
                            undefined,
                            4
                        )}
                        </pre>

                        <b> API Request Body: </b>
                        <pre> ${JSON.stringify(req.body, undefined, 4)} </pre>

                        <b> Reason: </b> ${reason}
                    `
                }
            }

            await sendEmail(
                payload.to,
                payload.subject,
                payload.data,
                payload.cc,
                payload.from
            )
        }
    }
    catch (error) {
        logger.error(error, {
            errorMessage: "Error in sending update customer(s) failure email"
        })
    }
}

exports.SAPServer = async (
    message,
    tenantId,
    errors
) => {
    if (VALUES.IS_DEV_ENV) {
        return
    }

    const reason =
        typeof errors === "object" && !Array.isArray(errors)
            ? (`
                    <pre>
                        ${JSON.stringify(errors, undefined, 4)}
                    </pre>
                    `
            )
            : errors

    const payload = {
        "from": EMAIL_ID.NOTIFICATION,
        "to": EMAIL_ID.FARIS,
        "cc": [
            EMAIL_ID.YOUSEF,
            EMAIL_ID.YASHASHWINI,
            EMAIL_ID.AALAP,
            EMAIL_ID.FORAM,
            EMAIL_ID.RAJAN,
        ],
        "subject": `Error on SAP Service`,
        "data": {
            "html": `
                    ${message}
                    <br/><br/>

                    <b> Environment: </b> ${VALUES.ENVIRONMENT}
                    <br/><br/>

                    <b> Tenant Id: </b> ${tenantId}
                    <br/><br/>

                    <b> Reason: </b> ${reason}
                    `
        }
    }

    await sendEmail(
        payload.to,
        payload.subject,
        payload.data,
        payload.cc,
        payload.from
    )
}
