
const moment = require('moment');

// Models
const RewardProgramPointsLogSchema = require('../Database/Schemas/RewardProgram/RewardProgramPointsLogSchema');
const RewardProgramMemberSchema = require('../Database/Schemas/RewardProgram/RewardProgramMemberSchema');
const RewardProgramMemberModel = new (require('../Models/RewardProgram/RewardProgramMemberModel'))();
const RewardProgramPointsLogModel = new (require('../Models/RewardProgram/RewardProgramPointsLogModel'))();
const RewardProgramMemberExpiryPointsModel = new (require('../Models/RewardProgram/RewardProgramMemberExpiryPointsModel'))();
const RewardProgramNotificationModel = new (require('../Models/RewardProgram/RewardProgramNotificationModel'))();
const SAPServiceModel = new (require('../Models/SAPServiceModel'))();
const CommonModel = new (require("../Models/common"))()

// Controllers
const RewardProgramController = new (require('../Controllers/RewardProgram/RewardProgramController'))();

// Constants
const { ENTITY_STATUS, REWARD_PROGRAM, SAP_SERVICE } = require('../Configs/constants');
const { sendFailureEmailSAPServer } = require('../Utils/helpers');
const { logRewardProgram } = require("../Utils/logHelper");

// Configuration
const CONFIG = {
    BATCH_SIZE: 75,
    API_DELAY: 1500,
    MAX_RETRIES: 3,
    RETRY_DELAY: 2000,
    MAX_CONCURRENT_MEMBERS: 10,
    PROGRESS_LOG_INTERVAL: 150,
    DATE_CHUNK_DAYS: 30, // Process dates in 30-day chunks
    AMOUNT_TOLERANCE: 0.01
};

class ComprehensiveRewardPointsProcessor {
    constructor() {
        this.stats = {
            totalMembers: 0,
            processedMembers: 0,
            totalPoints: 0,
            addedPoints: 0,
            duplicatesSkipped: 0,
            errors: 0,
            apiCalls: 0,
            expiryPointsCreated: 0
        };
        this.processedCustomers = new Set();
        this.cache = {
            rewardPrograms: new Map(),
            sapCredentials: new Map(),
            timezones: new Map()
        };
    }

    async run() {
        try {
            const startTime = Date.now();
            logger.info('🚀 Starting comprehensive reward points processing...');

            // Calculate date range
            const dateRange = await this.calculateDateRange();
            logger.info(`📅 Processing date range: ${dateRange.fromDate} to ${dateRange.toDate}`);

            // Process all reward programs
            await this.processAllRewardPrograms(dateRange);

            const duration = (Date.now() - startTime) / 1000;
            logger.info(`✅ Script completed in ${duration}s. Final stats:`, this.stats);

            return { success: true, stats: this.stats };

        } catch (error) {
            logger.error('❌ Script failed:', error);
            return { success: false, error: error.message, stats: this.stats };
        }
    }

    async calculateDateRange() {
        try {
            // Find earliest log_date from existing points logs
          

            const fromDate = "20250215";

            const toDate =  "20250317";

            logger.info(`📊 Date range calculated - From: ${fromDate}, To: ${toDate}`);

            return { fromDate, toDate };

        } catch (error) {
            logger.error('Error calculating date range:', error);
            throw error;
        }
    }

    async processAllRewardPrograms(dateRange) {
        const processRewardProgram = async (rewardProgram, sapIntegrationCredentials, timezone) => {
            try {
                logger.info(`🎯 Processing reward program: ${rewardProgram._id} for tenant: ${rewardProgram.tenant_id}`);

                // Cache data for reuse
                this.cache.rewardPrograms.set(rewardProgram._id.toString(), rewardProgram);
                this.cache.sapCredentials.set(rewardProgram.tenant_id, sapIntegrationCredentials);
                this.cache.timezones.set(rewardProgram.tenant_id, timezone);

                // Process in date chunks to manage memory and API limits
                await this.processRewardProgramInDateChunks(rewardProgram, sapIntegrationCredentials, timezone, dateRange);

                logger.info(`✅ Completed reward program: ${rewardProgram._id}`);

            } catch (error) {
                logger.error(`❌ Error processing reward program ${rewardProgram._id}:`, error);
                this.stats.errors++;
            }
        };

        await RewardProgramController.rewardProgramsCallBackBasedTenants(processRewardProgram, true);
    }

    async processRewardProgramInDateChunks(rewardProgram, sapCredentials, timezone, dateRange) {
        const { fromDate, toDate } = dateRange;
        const startMoment = moment(fromDate.toString(), 'YYYYMMDD');
        const endMoment = moment(toDate.toString(), 'YYYYMMDD');

        logger.info(`📅 Date chunking setup - Start: ${startMoment.format('YYYY-MM-DD')}, End: ${endMoment.format('YYYY-MM-DD')}, Chunk size: ${CONFIG.DATE_CHUNK_DAYS} days`);

        // Process in chunks
        let currentStart = startMoment.clone();
        let chunkNumber = 1;

        while (currentStart.isSameOrBefore(endMoment)) {
            try {
                const currentEnd = moment.min(
                    currentStart.clone().add(CONFIG.DATE_CHUNK_DAYS, 'days'),
                    endMoment
                );

                const chunkFromDate = parseInt(currentStart.format("YYYYMMDD"));
                const chunkToDate = parseInt(currentEnd.format("YYYYMMDD"));

                logger.info(`📦 Processing date chunk ${chunkNumber}: ${chunkFromDate} to ${chunkToDate} for program ${rewardProgram._id}`);
                logger.info(`📅 Chunk dates: ${currentStart.format('YYYY-MM-DD')} to ${currentEnd.format('YYYY-MM-DD')}`);

                await this.processRewardProgramMembers(
                    rewardProgram,
                    sapCredentials,
                    timezone,
                    chunkFromDate,
                    chunkToDate
                );

                logger.info(`✅ Completed chunk ${chunkNumber} for program ${rewardProgram._id}`);

                // Advance to next chunk - CRITICAL: Move to the day AFTER currentEnd
                currentStart = currentEnd.clone().add(1, 'day');
                chunkNumber++;

                logger.info(`➡️ Next chunk will start from: ${currentStart.format('YYYY-MM-DD')}, Loop condition: ${currentStart.isSameOrBefore(endMoment)}`);

                // Add delay between chunks only if there are more chunks to process
                if (currentStart.isSameOrBefore(endMoment)) {
                    logger.info(`⏳ Adding ${CONFIG.API_DELAY}ms delay before next chunk...`);
                    await this.delay(CONFIG.API_DELAY);
                }

            } catch (error) {
                logger.error(`❌ Error processing chunk ${chunkNumber} (${currentStart.format('YYYY-MM-DD')}):`, error);
                this.stats.errors++;
                
                // Continue to next chunk even if current chunk fails
                const currentEnd = moment.min(
                    currentStart.clone().add(CONFIG.DATE_CHUNK_DAYS, 'days'),
                    endMoment
                );
                currentStart = currentEnd.clone().add(1, 'day');
                chunkNumber++;
                
                logger.info(`⚠️ Skipping to next chunk starting from: ${currentStart.format('YYYY-MM-DD')}`);
            }
        }

        logger.info(`🏁 Completed all ${chunkNumber - 1} date chunks for program ${rewardProgram._id}`);
    }

    async processRewardProgramMembers(rewardProgram, sapCredentials, timezone, fromDate, toDate) {
        const rewardProgramMembersFilter = {
            rewardProgramId: rewardProgram._id,
            tenantId: rewardProgram.tenant_id,
            status: ENTITY_STATUS.ACTIVE
        };

        // Get total count using schema directly (with proper field names)
        const schemaFilter = {
            reward_program_id: rewardProgram._id,
            tenant_id: rewardProgram.tenant_id,
            is_enrolled: true,
            is_deleted: false

        };

        try {
            const totalMembers = await RewardProgramMemberSchema.countDocuments(schemaFilter);
            this.stats.totalMembers += totalMembers;
            logger.info(`👥 Processing ${totalMembers} members for date range ${fromDate}-${toDate}`);
        } catch (error) {
            logger.warn('Could not get member count:', error.message);
            logger.info(`👥 Processing members for date range ${fromDate}-${toDate}`);
        }

        await RewardProgramMemberModel.membersCallBackWithPagination(
            rewardProgramMembersFilter,
            this.processMemberWithRetry(fromDate, toDate, rewardProgram, sapCredentials, timezone),
            CONFIG.BATCH_SIZE
        );
    }

    processMemberWithRetry = (fromDate, toDate, rewardProgram, sapCredentials, timezone) => {
        return async (memberInfo) => {
            let retries = 0;

            while (retries < CONFIG.MAX_RETRIES) {
                try {
                    await this.processMember(memberInfo, fromDate, toDate, rewardProgram, sapCredentials, timezone);
                    break;
                } catch (error) {
                    retries++;
                    logger.warn(`⚠️ Retry ${retries}/${CONFIG.MAX_RETRIES} for member ${memberInfo._id}:`, error.message);

                    if (retries >= CONFIG.MAX_RETRIES) {
                        logger.error(`❌ Failed to process member ${memberInfo._id} after ${CONFIG.MAX_RETRIES} retries`);
                        this.stats.errors++;
                    } else {
                        await this.delay(CONFIG.RETRY_DELAY * retries);
                    }
                }
            }
        };
    };

    async processMember(memberInfo, fromDate, toDate, rewardProgram, sapCredentials, timezone) {
        const customerExternalId = memberInfo.customer_user_role_id?.external_id;

        if (!customerExternalId) {
            logger.warn(`No external_id found for member ${memberInfo._id}`);
            return;
        }

        const customerKey = `${rewardProgram.tenant_id}-${customerExternalId}-${fromDate}-${toDate}`;

        // Skip if already processed in this run
        if (this.processedCustomers.has(customerKey)) {
            return;
        }

        this.processedCustomers.add(customerKey);
        this.stats.processedMembers++;

        // Log progress (remove totalMembers reference if not available)
        if (this.stats.processedMembers % CONFIG.PROGRESS_LOG_INTERVAL === 0) {
            logger.info(`📊 Progress: ${this.stats.processedMembers} members processed`);
        }

        // Get points calculations from SAP
        const { points, errors } = await this.getPointCalculationsByCustomerInfo(
            customerExternalId,
            fromDate,
            toDate,
            memberInfo.membership,
            rewardProgram,
            sapCredentials,
            memberInfo._id
        );

        this.stats.totalPoints += points.length;

        // Process each point
        for (const point of points) {
            try {
                await this.processPoint(point, memberInfo, rewardProgram, timezone);
            } catch (error) {
                logger.error(`Error processing point for member ${memberInfo._id}:`, error);
                this.stats.errors++;
            }
        }

        // Handle errors
        if (errors.length) {
            await sendFailureEmailSAPServer(
                "Found some errors on statements from SAP",
                rewardProgram.tenant_id,
                { customerExternalId, errors }
            );
        }

        logRewardProgram(`Processed member ${memberInfo._id}`, {
            tenantId: rewardProgram.tenant_id,
            memberId: memberInfo._id,
            customerExternalId,
            pointsFound: points.length,
            errorsFound: errors.length
        });
    }

    async processPoint(point, memberInfo, rewardProgram, timezone) {
        // Check for duplicates
        const isDuplicate = await this.checkExactEntryExists(point, memberInfo, rewardProgram);
        logger.info(`isDuplicate: ${isDuplicate}`);
        if (isDuplicate.length) {
            this.stats.duplicatesSkipped++;
            logRewardProgram(`Skipping duplicate entry for member for script ${memberInfo._id}`, {
                tenantId: rewardProgram.tenant_id,
                memberId: memberInfo._id,
                point
            });
            return;
        }

        try {
            // Add point to database
            const logDate = moment.tz(point.log_date, timezone).startOf('day').format();

            const pointLogEntry = await this.addPointsLog(
                {
                    tenantId: rewardProgram.tenant_id,
                    customerUserRoleId: memberInfo.customer_user_role_id._id,
                    rewardProgramMemberId: memberInfo._id,
                    rewardProgramId: rewardProgram._id,
                    entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                    pointType: point.point_type,
                    logType: point.log_type,
                    logDate: logDate,
                    calculations: point.calculations,
                    points: point.points,
                    amount: point.amount,
                    documentNumber: point.document_number ? point.document_number.join(', ') : null
                },
                memberInfo
            );

            this.stats.addedPoints++;


            logRewardProgram(`Added points for member from script ${memberInfo._id}`, {
                tenantId: rewardProgram.tenant_id,
                memberId: memberInfo._id,
                point,
                pointLogId: pointLogEntry._id
            });

        } catch (error) {
            // Handle database constraint errors
            if (error.code === 11000) {
                this.stats.duplicatesSkipped++;
                logRewardProgram(`Database duplicate key error for member ${memberInfo._id}`, {
                    tenantId: rewardProgram.tenant_id,
                    memberId: memberInfo._id,
                    point,
                    error: error.message
                });
            } else {
                this.stats.errors++;
                throw error;
            }
        }
    }

    async checkExactEntryExists(point, memberInfo, rewardProgram) {
        try {
            // Handle documentNumber with proper type checking and comma-separated values
            let documentNumberFilter = null;

            if (point.document_number != null) {
                const tokens = String(point.document_number)
                    .split(",")
                    .map(t => t.trim())
                    .filter(Boolean);

                // Escape regex special characters
                const escapeRegex = (s) => s.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
                
                // Match any token as a whole item in a comma-separated string
                const pattern = `(?:^|,\\s*)(?:${tokens.map(escapeRegex).join("|")})(?=\\s*(?:,|$))`;

                // Covers both cases:
                // 1) Field is a plain string like "39286, 39281" (matched by $regex)
                // 2) Field equals exactly one token (matched by $in)
                documentNumberFilter = {
                    $or: [
                        { document_number: { $regex: pattern } },
                        { document_number: { $in: tokens } }
                    ]
                };
            }

            const queryFilter = {
                tenant_id: rewardProgram.tenant_id,
                customer_user_role_id: memberInfo.customer_user_role_id._id,
                reward_program_member_id: memberInfo._id,
                reward_program_id: rewardProgram._id,
                entry_type: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                point_type: point.point_type,
                amount: point.amount
            };

            // Only add documentNumber to filter if it exists
            if (documentNumberFilter !== null) {
                Object.assign(queryFilter, documentNumberFilter);
            }

            const existingEntry = await RewardProgramPointsLogModel.findPointsLogsByFilter(queryFilter);
            return existingEntry;

        } catch (error) {
            logger.error('Error checking duplicate entry:', error);
            return [];
        }
    }
    async getPointCalculationsByCustomerInfo(
        customerExternalId,
        fromDate,
        toDate,
        membership,
        rewardProgram,
        sapIntegrationCredentials,
        memberId
    ) {
        let points = [];
        let errors = [];

        if (!customerExternalId) {
            logRewardProgram(`No customer external id found for member ${memberId}`, {
                tenantId: rewardProgram.tenant_id,
                memberId,
                fromDate
            });
            return { points, errors };
        }

        try {
            this.stats.apiCalls++;

            const statementsInfo = await SAPServiceModel.getStatements(
                { customerExternalId, fromDate, toDate },
                sapIntegrationCredentials
            );

            if (!statementsInfo?.statements) {
                return { points, errors };
            }

            const statementsSummaryWithDate = SAPServiceModel.generateStatementsSummery(statementsInfo.statements);
            const statementsDates = Object.keys(statementsSummaryWithDate);

            logRewardProgram(`Found statements for ${statementsDates.length} dates`, {
                tenantId: rewardProgram.tenant_id,
                memberId,
                customerExternalId,
                statementsDates
            });

            for (const statementsDate of statementsDates) {
                const agingDate = parseInt(moment(statementsDate, "YYYY/MM/DD").subtract(1, "day").format("YYYYMMDD"));
                const statementsSummary = statementsSummaryWithDate[statementsDate];

                let aging = null;

                if ((statementsSummary[SAP_SERVICE.DOCUMENT_TYPE.PAYMENT]?.credit_amount ?? 0) > 0) {
                    this.stats.apiCalls++;
                    aging = await SAPServiceModel.getBalance(
                        { customerExternalId, agingDate },
                        sapIntegrationCredentials
                    );
                    if (aging) {
                        aging.date = agingDate;
                    }
                }

                RewardProgramPointsLogModel.calculatePointsFromStatementSummery(
                    points,
                    errors,
                    statementsSummary,
                    statementsDate,
                    aging,
                    membership,
                    rewardProgram,
                    memberId,
                    fromDate,
                    customerExternalId
                );
            }

        } catch (error) {
            logger.error(`Error getting points for customer ${customerExternalId}:`, error);
            errors.push(`API Error: ${error.message}`);
        }

        return { points, errors };
    }

    addPointsLog = async (
        body,
        memberInfo,
        userId,
        session
    ) => {
        const {
            tenantId,
            rewardProgramId,
            rewardProgramMemberId,
            customerUserRoleId,
            productClaimId,
            points,
            logType,
            calculations,
            entryType,
            pointType,
            logDate,
            amount,
            description,
            documentNumber
        } = body

        const newRewardPointsLog = new RewardProgramPointsLogSchema(
            {
                tenant_id: tenantId,
                customer_user_role_id: customerUserRoleId,
                reward_program_member_id: rewardProgramMemberId,
                reward_program_id: rewardProgramId,
                point_type: pointType,
                entry_type: entryType,
                log_type: logType,
                product_claim_id: productClaimId,
                calculations,
                points,
                amount,
                log_date: logDate,
                description,
                document_number: documentNumber,
                created_by: userId,
                updated_by: userId
            }
        )

        // const logPoint = (info) => {
        //     logRewardProgram(`For ${info.log_type}, ${info.entry_type} ${info.points} ${info.point_type}. Reward memberId: ${info.reward_program_member_id}`, {
        //         tenantId,
        //         logDate,
        //         rewardProgramId,
        //         rewardProgramMemberId,
        //         customerUserRoleId,
        //         productClaimId,
        //     })
        // }

        const callBack = async (session) => {
            switch (pointType) {
                case REWARD_PROGRAM.POINT.TYPE.COINS: {
                    switch (entryType) {
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED: {
                            if (logType === REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.REFUND) {
                                memberInfo.coins.claimed -= points
                            }

                            let updatedCoins = memberInfo.coins.remaining + points

                            for (let i = 1; i <= memberInfo.reward_program_id.milestones.length; i++) {
                                const {
                                    coins_required,
                                    bonus_coins,
                                    _id: milestoneId
                                } = memberInfo.reward_program_id.milestones[i - 1];

                                if (
                                    memberInfo.coins.remaining < coins_required &&
                                    updatedCoins >= coins_required
                                ) {
                                    const pointsLogs = await this.findPointsLogsByFilter(
                                        {
                                            tenant_id: tenantId,
                                            customer_user_role_id: customerUserRoleId,
                                            log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SYSTEM.MILESTONES,
                                            milestone_id: milestoneId,
                                            point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                                            entry_type: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                                        },
                                        undefined,
                                        {
                                            session,
                                            lean: true
                                        }
                                    )

                                    if (!pointsLogs.length) {
                                        const milestonePointLog = new RewardProgramPointsLogSchema({
                                            tenant_id: tenantId,
                                            customer_user_role_id: customerUserRoleId,
                                            reward_program_member_id: rewardProgramMemberId,
                                            reward_program_id: rewardProgramId,
                                            point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                                            entry_type: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                                            log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SYSTEM.MILESTONES,
                                            milestone_id: milestoneId,
                                            points: bonus_coins,
                                            log_date: logDate,
                                            description,
                                            created_by: userId,
                                            updated_by: userId
                                        })

                                        await milestonePointLog.save({ session })
                                       // logPoint("milestonePointLog", milestonePointLog)
                                        await RewardProgramNotificationModel.sentRewardPointNotification(milestonePointLog, session, i)

                                        const statisticsInfos = RewardProgramMemberModel.getStatisticsInfoFormPointsLog(milestonePointLog)
                                        statisticsInfos.forEach(statisticsInfo => {
                                            memberInfo[statisticsInfo.pointKey][statisticsInfo.key] += statisticsInfo.points
                                        })

                                        updatedCoins += bonus_coins
                                    }
                                }
                            }

                            const expireDate = moment(logDate).add(1, "years")
                            await RewardProgramMemberExpiryPointsModel.upsertExpiryPoints(
                                {
                                    tenant_id: tenantId,
                                    customer_user_role_id: customerUserRoleId,
                                    reward_program_member_id: rewardProgramMemberId,
                                    reward_program_id: rewardProgramId,
                                    point_type: pointType,
                                    expiry_year: parseInt(expireDate.format("YYYY")),
                                    expiry_month: parseInt(expireDate.format("MM")),
                                },
                                {
                                    "$inc": {
                                        points: updatedCoins - memberInfo.coins.remaining
                                    }
                                },
                                {
                                    session
                                }
                            )

                            memberInfo.coins.remaining = updatedCoins

                            break;
                        }
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.CLAIMED: {
                            memberInfo.coins.claimed += points
                            memberInfo.coins.remaining -= points

                            await RewardProgramMemberExpiryPointsModel.destroyExpiryPoints(
                                points,
                                pointType,
                                rewardProgramMemberId,
                                tenantId,
                                session
                            )

                            break;
                        }
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED: {
                            memberInfo.coins.expired += points
                            memberInfo.coins.remaining -= points
                            break;
                        }
                    }
                    break;
                }
                case REWARD_PROGRAM.POINT.TYPE.VIP_POINTS: {
                    switch (entryType) {
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED: {
                            memberInfo.vip_points.remaining += points

                            if (logType === REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.REFUND) {
                                memberInfo.vip_points.claimed -= points
                            }
                            break;
                        }
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.CLAIMED: {
                            memberInfo.vip_points.claimed += points
                            memberInfo.vip_points.remaining -= points
                            break;
                        }
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED: {
                            memberInfo.vip_points.expired += points
                            memberInfo.vip_points.remaining -= points
                            break;
                        }
                    }
                    break;
                }
            }

            await newRewardPointsLog.save({ session })
            //logPoint("newRewardPointsLog", newRewardPointsLog)
            await RewardProgramNotificationModel.sentRewardPointNotification(newRewardPointsLog, session)

            const statisticsInfos = RewardProgramMemberModel.getStatisticsInfoFormPointsLog(newRewardPointsLog)
            statisticsInfos.forEach(statisticsInfo => {
                memberInfo[statisticsInfo.pointKey][statisticsInfo.key] += statisticsInfo.points
            })

            await memberInfo.save({ session })
        }

        if (session) {
            await callBack(session)
        }
        else {
            await CommonModel.transactionCallback(callBack)
        }

        return newRewardPointsLog
    }

    findPointsLogsByFilter = (filter, projection, options) => {
        return RewardProgramPointsLogSchema.find(
            filter,
            projection,
            options ?? {
                lean: true
            }
        )
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Main execution
async function main() {
    try {
        logger.info('🚀 Starting comprehensive reward points processor script...');

        const processor = new ComprehensiveRewardPointsProcessor();
        const result = await processor.run();

        if (result.success) {
            logger.info('✅ Script completed successfully!', result.stats);
            process.exit(0);
        } else {
            logger.error('❌ Script failed:', result.error);
            process.exit(1);
        }

    } catch (error) {
        logger.error('💥 Unexpected error:', error);
        process.exit(1);
    }
}

// Export for testing or manual execution
module.exports = { ComprehensiveRewardPointsProcessor, main };

// Run if called directly
if (require.main === module) {
    main();
}
