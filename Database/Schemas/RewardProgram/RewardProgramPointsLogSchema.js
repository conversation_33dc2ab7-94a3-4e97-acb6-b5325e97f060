const Schema = mongoose.Schema;

const {
    stringFromObject
} = require("../../../Utils/helpers")

const {
    REWARD_PROGRAM,
} = require("../../../Configs/constants")

const CalculationsInfoSchema = new Schema(
    {
        type: {
            type: String,
            required: true,
            trim: true,
            enum: stringFromObject(REWARD_PROGRAM.POINT.CALCULATION_TYPE),
        },
        points: {
            type: Number,
            required: true,
        },
        amountUsed: {
            type: Number,
            required: true,
        },
        aging: {
            type: Number,
            required: true,
        },
    },
    {
        _id: false
    }
)

const RewardProgramPointsLogSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        customer_user_role_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "user_roles",
            required: true,
        },
        reward_program_member_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_program_members",
            required: true,
        },
        reward_program_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_programs",
            required: true,
        },
        milestone_id: {
            type: mongoose.Schema.Types.ObjectId,
        },
        product_claim_id: {
            type: mongoose.Schema.Types.ObjectId,
        },
        point_type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(REWARD_PROGRAM.POINT.TYPE),
        },
        entry_type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(REWARD_PROGRAM.POINT.ENTRY_TYPE),
        },
        log_type: {
            type: String,
            required: true,
            trim: true,
            enum: stringFromObject(REWARD_PROGRAM.POINT.LOG_TYPE),
        },
        calculations: {
            type: [CalculationsInfoSchema],
        },
        points: {
            type: Number,
            required: true,
        },
        description: {
            type: String,
            trim: true
        },
        document_number: {
            type: String,
            trim: true
        },
        log_date: {
            type: Date,
            default: () => new Date(),
        },
        amount: {
            type: Number,
        },
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

RewardProgramPointsLogSchema.index({
    tenant_id: 1,
    reward_program_id: 1,
    reward_program_member_id: 1,
    point_type: 1,
    createdAt: -1,
})

module.exports = mongoose.model('reward_program_points_logs', RewardProgramPointsLogSchema);
