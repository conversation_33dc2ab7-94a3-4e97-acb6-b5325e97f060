const Schema = mongoose.Schema;

const BranchesSchema = new Schema(
    {
        branch_id: {
            type: Schema.Types.ObjectId,
            ref: "tenant_branches",
            required: true,
        },
        name: {
            type: String,
            required: true,
        },
        street_address: {
            type: String,
            required: true
        },
        region: {
            type: Schema.Types.ObjectId,
            ref: "regions",
            required: true
        },
        city: {
            type: Schema.Types.ObjectId,
            ref: "cities",
            required: true
        },
        phone_number: {
            type: Number,
        },
        phone_number_country_code: {
            type: String,
        },
        mobile_number: {
            type: Number,
            required: true
        },
        mobile_number_country_code: {
            type: String,
            required: true,
        },
    },
    {
        _id: false
    }
)

const ShippingLabelSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
            ref: 'tenants',
        },
        tenant_legal_name: {
            type: String,
            required: true
        },
        branches: {
            type: [BranchesSchema],
            validate: v => Array.isArray(v) && v.length > 0,
            required: true
        },
        shipping_label_logo: {
            type: String,
            //required: true,
            default: null,
        },
        is_active: {
            type: Boolean,
            required: true,
            default: true,
        },
        created_by: {
            type: Schema.Types.ObjectId,
            ref: 'users',
            required: true
        },
        updated_by: {
            type: Schema.Types.ObjectId,
            ref: 'users',
            required: true
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

module.exports = mongoose.model("shipping_labels", ShippingLabelSchema);
