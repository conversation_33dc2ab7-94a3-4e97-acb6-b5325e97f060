# Postman Documentation: Update User Role Settings

## Endpoint Information

- **Method**: `POST`
- **URL**: `{{base_url}}/dataSync/userRoleSettings`
- **Description**: Updates user role settings for a specific tenant and user role combination
- **Category**: DataSync

## Authentication & Authorization

### Required Headers
Authorization:{{Authorization}}
devicetoken:{{devicetoken}}
refreshToken:{{refreshToken}}
userroleid:{{userRoleId}}


## Request Body

### Required Fields
```json
{
    "tenant_id": 1131,
    "user_role_id": "6621333850e6570012327222",
    "updated_at": "2025-08-24T06:06:48.191Z"
}
```

### Optional Fields
```json
{
    "default_master_price_id": "6374bd481f6d6bdec112ee96",
    "out_of_stock": {
        "visible": true,
        "searchable": false
    },
    "preferred_language": "en",
    "price_change": true
}
```

### Field Descriptions

| Field | Type | Required | Description | Example |
|-------|------|----------|-------------|---------|
| `tenant_id` | Integer | Yes | Tenant identifier (must be ≥ 1000) | `1131` |
| `user_role_id` | String | Yes | MongoDB ObjectId of the user role | `"6621333850e6570012327222"` |
| `updated_at` | String | Yes | ISO 8601 timestamp for conflict resolution | `"2025-08-24T06:06:48.191Z"` |
| `default_master_price_id` | String | No | MongoDB ObjectId of the default master price list | `"6374bd481f6d6bdec112ee96"` |
| `out_of_stock` | Object | No | Configuration for out-of-stock visibility | `{"visible": true, "searchable": false}` |
| `out_of_stock.visible` | Boolean | No | Whether out-of-stock items are visible | `true` |
| `out_of_stock.searchable` | Boolean | No | Whether out-of-stock items are searchable | `false` |
| `preferred_language` | String | No | User's preferred language code | `"en"` |
| `price_change` | Boolean | No | Whether price changes are allowed | `true` |

## Example Request

### Complete Request Body
```json
{
    "_id": "1131_6621333850e6570012327222",
    "default_master_price_id": "6374bd481f6d6bdec112ee96",
    "out_of_stock": {
        "visible": true,
        "searchable": false
    },
    "preferred_language": "en",
    "price_change": true,
    "tenant_id": 1131,
    "updated_at": "2025-08-24T06:06:48.191Z",
    "user_role_id": "6621333850e6570012327222"
}
```

### cURL Example
```bash
curl --location '{{base_url}}/dataSync/userRoleSettings' \
--header 'Authorization: {{Authorization}}' \
--header 'devicetoken: {{devicetoken}}' \
--header 'userroleid: {{userRoleId}}' \
--header 'refreshToken : {{refreshToken}}'\
--header 'Content-Type: application/json' \
--data '{
    "tenant_id": 1131,
    "user_role_id": "6621333850e6570012327222",
    "default_master_price_id": "6374bd481f6d6bdec112ee96",
    "out_of_stock": {
        "visible": true,
        "searchable": false
    },
    "preferred_language": "en",
    "price_change": true,
    "updated_at": "2025-08-24T06:06:48.191Z"
}'
```

## Response

### Success Response (200)
```json
{
    "message": "status_success",
    "data": {
       "success": true,
        "isUpToDate": true,
        "existingData": [],
        "message": "User role settings updated successfully"
    }
}
```

### Conflict Response (200) - Update Skipped
```json
{
    "message": "status_success",
    "data": {
        "success": true,
        "isUpToDate": false,
        "message": "Update skipped - existing data is more recent",
         "existingData": [
            {
                "out_of_stock": {
                    "visible": false,
                    "searchable": false
                },
                "_id": "1131_642bde213d13c3001234c043",
                "default_master_price_id": "6374c0b1d78429fdf5777314",
                "preferred_language": "en",
                "price_change": false,
                "tenant_id": 1131,
                "updated_at": "2025-08-29T12:24:08.487Z",
                "user_role_id": "642bde213d13c3001234c043"
            }
        ]
    }   
}
```

### Validation Error Response (422)
```json
{
    "message": "status_bad_request",
    "data" : [
        error_message
    ]
}
```

## Business Logic

### Conflict Resolution
The endpoint implements optimistic locking using the `updated_at` timestamp:
- If existing data has a more recent `updated_at` timestamp, the update is skipped
- This prevents overwriting newer data with older data
- The response includes information about why the update was skipped

### Upsert Behavior
- If the user role settings don't exist, they will be created
- If they exist, they will be updated with the provided fields
- Only the fields provided in the request body are updated

### Field Validation
- At least one field must be provided for update (excluding `tenant_id` and `user_role_id`)
- The `_id` field is automatically generated as `{tenant_id}_{user_role_id}`

## Error Handling

### Common Error Scenarios
1. **Missing Required Fields**: `tenant_id`, `user_role_id`, or `updated_at`
2. **Invalid MongoDB ObjectId**: `user_role_id` must be a valid MongoDB ObjectId
3. **Permission Denied**: User lacks required permissions
4. **Authentication Failed**: Invalid or missing authentication headers
5. **Device Validation Failed**: Invalid device token or session

