{"openapi": "3.0.0", "info": {"title": "prod_service", "description": "production environment server", "version": "2025-09-16T13:08:07.937Z"}, "servers": [{"url": "https://api.hawak.io/", "description": "production environment server"}], "tags": [{"name": "<PERSON><PERSON>"}, {"name": "Auth > Token Info"}, {"name": "Auth > App Auth"}, {"name": "Auth > Forgot Password/Reset Password"}, {"name": "Common"}, {"name": "Common > Countries"}, {"name": "Common > User"}, {"name": "Common > City"}, {"name": "Common > City > Chnage Status of Cities"}, {"name": "Common > Languages"}, {"name": "Common > Regions"}, {"name": "Common > Image"}, {"name": "Data Sync", "description": "This folder contains APIs used by the tablet app to fetch data for newly added and updated records."}, {"name": "Integration Credentials"}, {"name": "Languages"}, {"name": "Languages > Key"}, {"name": "Roles"}, {"name": "Roles > <PERSON><PERSON><PERSON>"}, {"name": "System Portal"}, {"name": "System Portal > Tenant branch"}, {"name": "System Portal > Tenants"}, {"name": "System Portal > Tenants > Linked tenants"}, {"name": "System Portal > Assigned Tenant"}, {"name": "System Portal > System Users"}, {"name": "System Portal > Dashboard"}, {"name": "System Portal > User"}, {"name": "Tenant Portal"}, {"name": "Tenant Portal > Branches"}, {"name": "Tenant Portal > Tenant Portal User"}, {"name": "Tenant Portal > Tenant Portal User > User"}, {"name": "Tenant Portal > Tenant Portal User > Users"}, {"name": "Tenant Portal > Tenant Portal User > User settings"}, {"name": "Tenant Portal > Tenant customer"}, {"name": "Tenant Portal > Tenant customer > Customer devices"}, {"name": "Tenant Portal > Tenant customer > Customer"}, {"name": "Tenant Portal > Tenant customer > Customers"}, {"name": "Tenant Portal > Supervisors"}, {"name": "Tenant Portal > Sales person"}, {"name": "Tenant Portal > Tenant price list"}, {"name": "Tenant Portal > Payment"}, {"name": "Tenant Portal > Payment Terms"}, {"name": "Tenant Portal > Tenant Configurations"}, {"name": "Tenant Portal > Tenant Allow Validation"}, {"name": "Tenant Portal > Datasheet"}, {"name": "Tenant Portal > Hold Reason"}, {"name": "Tenant Portal > Hold Reason > Hold Reason Template Options"}, {"name": "Tenant Portal > Statement"}, {"name": "Tenant Portal > Reward Program"}, {"name": "Tenant Portal > Reward Program > Calculator"}, {"name": "Tenant Portal > Reward Program > Program"}, {"name": "Tenant Portal > Reward Program > Configuration"}, {"name": "Tenant Portal > Reward Program > Members"}, {"name": "Tenant Portal > Reward Program > Points"}, {"name": "Tenant Portal > Reward Program > Product"}, {"name": "Tenant Portal > Reward Program > Product Claims"}, {"name": "Tenant Portal > Reward Program > Dashboard"}, {"name": "Tenant Portal > Notification"}, {"name": "Tenant Portal > Notification > Schedule"}], "paths": {"/auth/token-validity": {"get": {"tags": ["Auth > Token Info"], "summary": "Get token info", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{useRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Auth > Token Info"], "summary": "Update token validity", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"time": 5, "timeUnit": "hours"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/app-sign-in": {"post": {"tags": ["Auth > App Auth"], "summary": "<PERSON>pp <PERSON>gin", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"countryCode": "+91", "mobileNumber": "7685493020"}}}}}, "parameters": [{"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "description": "TABLET / MOBILE", "example": "{{deviceaccesstype}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "description": "IOS / ANDROID", "example": "{{devicetype}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/verify-auth-otp": {"post": {"tags": ["Auth > App Auth"], "summary": "Verify the OTP", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"countryCode": "+91", "mobileNumber": "7685493020", "session": "6863c0a1726c81a13af7db09", "otp": "0012"}}}}}, "parameters": [{"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "example": "{{deviceaccesstype}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/app-user-roles": {"post": {"tags": ["Auth > App Auth"], "summary": "Get app user roles", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"countryCode": "+91", "mobileNumber": "7685493020"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "example": "{{deviceaccesstype}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/app-resend-otp": {"post": {"tags": ["Auth > App Auth"], "summary": "App resend otp", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"session": "{{app_session}}}"}}}}}, "parameters": [{"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "IOS"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "example": "TABLET"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/forgot-password": {"post": {"tags": ["Auth > Forgot Password/Reset Password"], "summary": "Forgot password", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"sendOtpTo": "MOBILE", "mobileNumber": "90332777923", "countryCode": "+91"}}}}}, "parameters": [{"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/change-password-with-token": {"post": {"tags": ["Auth > Forgot Password/Reset Password"], "summary": "Verify forgot password with Change password", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"token": "$2a$10$uxXEVWeFqlEf5B/tc4x.eO5y.H1K6Mqxo31E5uLU3gJQGDo5NYUUW", "id": "637cc5c88ef14a0012cdc62c", "newPassword": "Test@1234", "confirmPassword": "Test@1234", "type": "EMAIL"}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/verify-mobile-forgot-password-otp": {"post": {"tags": ["Auth > Forgot Password/Reset Password"], "summary": "Verify mobile otp for forgot password", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"token": "2886", "mobileNumber": "+918460353552"}}}}}, "parameters": [{"name": "", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/check-rest-password-link": {"post": {"tags": ["Auth > Forgot Password/Reset Password"], "summary": "Check rest password token", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"token": "$2a$10$.Nb1cEV5ZSlX3rBmIliUtuLDmedJmocAikrII./megkNKGt0kB8HS", "id": "62e275ac0b77a6bbdf16fac8"}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/verify-user": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Verify user", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"confirmationCode": "9815"}}}}}, "parameters": [{"name": "deviceToken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/sign-in": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Sign in", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"email": "{{email}}", "password": "{{password}}", "type": "EMAIL"}}}}}, "parameters": [{"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/resend-otp": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Resend verification otp", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"sendOtpTo": "EMAIL"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/user-role-accessed": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Role accessed", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1004, "userRoleId": "658432bfc8e2c100126aafee", "fcmToken": "token"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "description": "iOS or ANDROID", "example": "{{devicetype}}"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "description": "TABLET or MOBILE", "example": "{{deviceaccesstype}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/user-roles": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get user roles(profiles)", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "deviceType", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "example": "{{deviceaccesstype}}"}, {"name": "userId", "in": "query", "schema": {"type": "string"}, "example": "6368f0ee3b253000115866a6"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/logout": {"put": {"tags": ["<PERSON><PERSON>"], "summary": "User logout", "requestBody": {"content": {}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/service-authenticator": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Service Authenticator", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/generateMapFile": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "G Map image generation", "parameters": [{"name": "lat", "in": "query", "schema": {"type": "number"}, "example": "0.0"}, {"name": "lng", "in": "query", "schema": {"type": "number"}, "example": "0.0"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/country": {"post": {"tags": ["Common > Countries"], "summary": "Add country", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"countryName": "india", "countryCode": "+91", "timeZone": "Asia/Kolkata", "mobileNumberFormat": 10, "currency": "₹", "isActive": true, "vat": 0, "alphaTwoCode": "IN", "secondaryLanguage": "hi", "secondaryLanguageName": ""}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Common > Countries"], "summary": "edit country", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"countryId": "62e7dacf077fb60012eb35e6", "countryName": "United Arab Emirates", "countryCode": "+971", "timeZone": "Asia/Dubai", "mobileNumberFormat": 10, "currency": "د.إ", "isActive": true, "vat": 1.2, "alphaTwoCode": "UAE", "secondaryLanguage": "ar"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Common > Countries"], "summary": "delete country", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "countryId", "in": "query", "schema": {"type": "string"}, "example": "62e7d842077fb60012eb35e3"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/get-countries": {"get": {"tags": ["Common > Countries"], "summary": "Get countries", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "description": "ALL  /  PAGINATION  /  SEARCH", "example": "PAGINATION"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "ind"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/country-status": {"put": {"tags": ["Common > Countries"], "summary": "change country status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"countryId": "62e7dacf077fb60012eb35e6", "status": "INACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/change-user-password": {"put": {"tags": ["Common > User"], "summary": "Update user password", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"type": "EMAIL_LINK", "userId": "62e3de95d533d300122ad48f", "password": "Test@1234"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/search-users": {"get": {"tags": ["Common > User"], "summary": "Search users", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "MOBILE"}, {"name": "mobileNumber", "in": "query", "schema": {"type": "integer"}, "example": "6352"}, {"name": "countryCode", "in": "query", "schema": {"type": "string"}, "example": "%2B91"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/user-profile-pic": {"put": {"tags": ["Common > User"], "summary": "Chnage user profile pic", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"userId": {"type": "string", "example": "6368f0ee3b253000115866a6"}, "tenantId": {"type": "integer", "example": "1131"}, "profilePic": {"type": "string", "description": "if you want to delete profile pic then do not send it in body", "format": "binary"}, "type": {"type": "string", "example": "SHIPPING_LABEL"}, "file": {"type": "string", "description": "if you want to delete file then do not send it in body", "format": "binary"}, "fileType": {"type": "string", "example": "image/png"}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/cities-status": {"put": {"tags": ["Common > City > Chnage Status of Cities"], "summary": "edit stauts od cities", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"cities": ["63202567f201ef00122bfa43"], "status": "ACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Common > City > Chnage Status of Cities"], "summary": "delete cities", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/city": {"post": {"tags": ["Common > City"], "summary": "Add City", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"regionId": "6303c222acf99987139a7275", "isActive": true, "name": "Ahmedabad", "countryId": "62e7d842077fb60012eb35e3"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Common > City"], "summary": "Edit city", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"regionId": "631ae5355eece300129f92da", "isActive": true, "name": "Rajkot", "countryId": "62e7d842077fb60012eb35e3", "cityId": "631ef726f540f900121c38b9"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Common > City"], "summary": "City listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "description": "ALL / SEARCH / PAGINATION", "example": "PAGINATION"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/automatic-add-city": {"post": {"tags": ["Common > City"], "summary": "Automatic add city ( mobile app )", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"countryName": "India", "regionName": "ka<PERSON>nataka", "cityName": "Mysore", "regionCode": "PIO"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/language": {"get": {"tags": ["Common > Languages"], "summary": "get languages", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "PAGINATION"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/region": {"post": {"tags": ["Common > Regions"], "summary": "add region", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"countryId": "62e7d842077fb60012eb35e3", "isActive": true, "name": "Madhya Pradesh", "code": "MP"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Common > Regions"], "summary": "edit region", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"code": "TRY", "countryId": "6319ca0f728e4b0012c04ff3", "isActive": true, "name": "TYR", "regionId": "631adc6acd06f700114b7c91"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Common > Regions"], "summary": "Get Region", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "description": "ALL  /  SEARCH  /  PAGINATION", "example": "PAGINATION"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "countryId", "in": "query", "schema": {"type": "string"}, "example": "62e7d842077fb60012eb35e3"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "ind"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Common > Regions"], "summary": "Delete Region", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "regionId", "in": "query", "schema": {"type": "string"}, "example": "6303c222acf99987139a7275"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/region-status": {"put": {"tags": ["Common > Regions"], "summary": "change region status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"regionId": "", "status": "INACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/addImage": {"post": {"tags": ["Common > Image"], "summary": "Add Image", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "imageName": "64c892d6d2e6f000120b590d.jpg"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/deleteImage": {"put": {"tags": ["Common > Image"], "summary": "Delete Image", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "imageName": "64c892d6d2e6f000120b590d.jpg"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/common/getUploadSignature": {"post": {"tags": ["Common"], "summary": "Get Upload Signature", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1241, "dataType": "PRODUCT", "operationType": "UPDATE", "updateType": "DETAILS"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/dataSync/tenantAppSetting": {"get": {"tags": ["Data Sync"], "summary": "Get Tenant App Setting", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "APP_SETTING"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/dataSync/userRoleSettings": {"get": {"tags": ["Data Sync"], "summary": "Get User Role Settings", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "recordType", "in": "query", "schema": {"type": "string"}, "description": "Either CREATED or UPDATED", "example": "CREATED"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}, {"name": "lastSyncedAt", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the ISOString Date", "example": "2025-06-17T12:27:54.926Z"}, {"name": "cursor", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the _id of the User Role Setting", "example": "1131_6621333850e6570012327222"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Data Sync"], "summary": "Update User Role Settings ", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "1131_6621333850e6570012327222", "default_master_price_id": "6374bd481f6d6bdec112ee96", "out_of_stock": {"visible": true, "searchable": false}, "preferred_language": "en", "price_change": true, "tenant_id": 1131, "updated_at": "2025-08-24T06:06:48.191Z", "user_role_id": "6621333850e6570012327222"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/dataSync/userRoles": {"get": {"tags": ["Data Sync"], "summary": "Get User Roles", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "recordType", "in": "query", "schema": {"type": "string"}, "description": "Either CREATED or UPDATED", "example": "CREATED"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/dataSync/regions": {"get": {"tags": ["Data Sync"], "summary": "Get Regions", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "countryId", "in": "query", "schema": {"type": "string"}, "example": "62e7d842077fb60012eb35e3"}, {"name": "recordType", "in": "query", "schema": {"type": "string"}, "description": "Either CREATED or UPDATED", "example": "CREATED"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/dataSync/cities": {"post": {"tags": ["Data Sync"], "summary": "Get Cities", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"regionIds": ["65c5ce577f6e6ac214b1a7aa", "65c5e776ab95bdbe8f8e940e"], "recordType": "CREATED", "perPage": 2, "lastSyncedAt": "2024-02-09T08:17:33.956Z", "cursor": "65c5df9d7f6e6ac214b1a7ac"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/integrationCredential": {"post": {"tags": ["Integration Credentials"], "summary": "Create Integration Credentials", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1241, "name": "SAP_SERVICE", "configurations": {"baseUrl": "https://www.google.com/", "username": "admin", "password": "admin"}, "isActive": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Integration Credentials"], "summary": "Get Integration Credentials", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1241"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Integration Credentials"], "summary": "Update Integration Credentials", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1241, "name": "SAP_SERVICE", "configurations": {"baseUrl": "https://c20182as01p01.cloudiax.com:8443/B1iXcellerator/exec/ipo/vP.001sap0000.in_HCSX/com.sap.b1i.vplatform.runtime/INB_HT_CALL_SYNC_XPT/INB_HT_CALL_SYNC_XPT.ipo/proc/report", "username": "<PERSON><PERSON><PERSON>", "password": "12345678"}, "isActive": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/language/key": {"post": {"tags": ["Languages > Key"], "summary": "Add Key API", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"key": "Testing_test_key1", "type": "both", "web": "", "tab": "", "both": "Testing_test_key"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Languages > Key"], "summary": "Edit key api", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"key": "VALIDATION.EXISTS.LANGUAGE_CODE###", "type": "web-tab", "web": "Popo", "tab": "Popo", "both": "", "hasTypeChanged": false, "languageCode": "chi", "languageId": "6369e1b60edf4ceb319129ce"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshtoken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/language": {"post": {"tags": ["Languages"], "summary": "Add language", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"translationFile": {"type": "string", "format": "binary"}, "languageCode": {"type": "string", "example": "chi"}, "name": {"type": "string", "example": "Mandarin"}, "enableRtl": {"type": "boolean", "example": "false"}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Languages"], "summary": "Edit Language", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"languageId": {"type": "string", "example": "6369e1b60edf4ceb319129ce"}, "enableRtl": {"type": "boolean", "example": "false"}, "translationFile": {"type": "string", "format": "binary"}, "languageCode": {"type": "string", "example": "chi"}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/language/sample-csv": {"get": {"tags": ["Languages"], "summary": "Get sample CSV API", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/role/add-module": {"post": {"tags": ["Roles > <PERSON><PERSON><PERSON>"], "summary": "Add module", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"name": "Localization", "portalType": "SYSTEM_PORTAL"}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/role/modules": {"get": {"tags": ["Roles > <PERSON><PERSON><PERSON>"], "summary": "Get portal modules", "parameters": [{"name": "portalType", "in": "query", "schema": {"type": "string"}, "example": "SYSTEM_PORTAL"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/role": {"post": {"tags": ["Roles"], "summary": "Add role", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"portalType": "SYSTEM_PORTAL", "name": "Operation head1", "description": "Has access to dashboard and tenants"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Roles"], "summary": "Edit Role", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"name": "Account manager", "roleId": "62ebb51d95a988f5121cf0d7", "description": "Testing description", "portalType": "SYSTEM_PORTAL"}}}}}, "parameters": [{"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Roles"], "summary": "Get roles", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "portalType", "in": "query", "schema": {"type": "string"}, "description": "Filter for portals", "example": "BRANCH_PORTAL"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Roles"], "summary": "Delete role", "parameters": [{"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "roleId", "in": "query", "schema": {"type": "string"}, "example": "62ebb51d95a988f5121cf0d7"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/role/edit-role-permission": {"put": {"tags": ["Roles"], "summary": "Edit Role permission", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"roleId": "62f33e4f15c14500125ca21b", "permission": {"Tenants": {"view": true, "edit": true, "delete": false, "create": false}, "Settings": {"view": false, "edit": false, "delete": false, "create": false}, "Localization": {"view": false, "edit": false, "delete": false, "create": false}, "System Users": {"create": false, "edit": false, "view": false, "delete": false}, "Dashboard": {"view": true, "edit": false, "delete": false, "create": false}}}}}}}, "parameters": [{"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/tenant-branch": {"post": {"tags": ["System Portal > Tenant branch"], "summary": "Add branch", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1018, "name": "OpenXcell"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["System Portal > Tenant branch"], "summary": "Update Branch", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"branchId": "630dc9f4f111c31fab351e82", "name": "chirag", "tenantId": 1000}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["System Portal > Tenant branch"], "summary": "Delete branch", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "branchId", "in": "query", "schema": {"type": "string"}, "example": "630dc9f4f111c31fab351e82"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1000"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/search-tenant": {"get": {"tags": ["System Portal > Tenants > Linked tenants"], "summary": "Search tenant", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "dia"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/linked-tenants": {"put": {"tags": ["System Portal > Tenants > Linked tenants"], "summary": "Add tenant as linked tenant", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1035, "LinkingTenantId": 1001}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["System Portal > Tenants > Linked tenants"], "summary": "Remove linked tenant", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1000"}, {"name": "LinkingTenantId", "in": "query", "schema": {"type": "integer"}, "example": "1001"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/tenant": {"post": {"tags": ["System Portal > Tenants"], "summary": "Add tenant", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"details": {"tenantName": "eferfe", "legalName": "ghvg", "streetName": "jhv", "country": "62e7d842077fb60012eb35e3", "region": "631ae5355eece300129f92da", "city": "64d486272825e600126986a2", "mobileNumber": "9898209898", "countryCode": "+91", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Dar<PERSON>"}, "services": {"serviceInfo": [{"key": "collections", "permission": {"view": true, "edit": true, "create": true, "delete": true, "key": "collections"}}, {"key": "customer_app", "permission": {"view": true, "edit": true, "create": true, "delete": true, "key": "customer_app"}}, {"key": "tracking", "permission": {"view": true, "edit": true, "create": true, "delete": true, "key": "tracking"}}, {"key": "deals", "permission": {"view": true, "edit": true, "create": true, "delete": true, "key": "deals"}}, {"key": "quotations", "permission": {"view": true, "edit": true, "create": true, "delete": true, "key": "quotations"}}, {"key": "push_notifications", "permission": {"view": true, "edit": true, "create": true, "delete": true, "key": "push_notifications"}}, {"key": "payments", "permission": {"view": true, "edit": true, "create": true, "delete": true, "key": "payments"}}], "enableSMSServices": false, "hasCustomSenderId": false, "smsSenderId": "LAWAZIM"}, "advanced": {"limits": [{"allowance": "56", "key": "NUMBER_OF_PRODUCTS"}, {"allowance": "12", "key": "NUMBER_OF_CUSTOMERS"}, {"allowance": "14", "key": "NUMBER_OF_USERS"}, {"allowance": "25", "key": "STORAGE_ALLOWANCE"}], "subscription": {}, "linkedTenants": []}, "branches": [{"branch_id": "B01", "name": "Main", "is_default": true}], "isActive": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["System Portal > Tenants"], "summary": "Get Tenant details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "tenantQueryType", "in": "query", "schema": {"type": "string"}, "example": "QUERY_AND_PROJECTION"}, {"name": "projection", "in": "query", "schema": {"type": "string"}, "example": "services"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["System Portal > Tenants"], "summary": "Update tenant", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"details": {"tenantName": "Kingsman", "legalName": "Kingsman Pvt Ltd", "streetName": "Kingsman Street", "country": "62e7d842077fb60012eb35e3", "region": "631ae5355eece300129f92da", "city": "63244af322e3eb003821e8ba", "mobileNumber": 8141600838, "countryCode": "+91", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Dar<PERSON>"}, "services": {"serviceInfo": [{"key": "collections", "permission": {"view": false, "create": false, "edit": false, "delete": false}}, {"key": "customer_app", "permission": {"view": true, "edit": true, "create": true, "delete": true}}, {"key": "tracking", "permission": {"view": true, "edit": true, "create": true, "delete": true}}, {"key": "deals", "permission": {"view": true, "edit": true, "create": true, "delete": true}}, {"key": "quotations", "permission": {"view": true, "edit": true, "create": true, "delete": true}}, {"key": "push_notifications", "permission": {"view": true, "edit": true, "create": true, "delete": true}}, {"key": "payments", "permission": {"view": true, "edit": true, "create": true, "delete": true}}]}, "advanced": {"limits": [{"allowance": 1349, "key": "NUMBER_OF_PRODUCTS"}, {"allowance": 200, "key": "NUMBER_OF_CUSTOMERS"}, {"allowance": 2000, "key": "NUMBER_OF_USERS"}, {"allowance": 2000, "key": "STORAGE_ALLOWANCE"}], "subscription": {"from": "2022-11-07T12:53:48.331Z", "to": "2023-02-07T00:00:00.000Z"}, "linkedTenants": [1237]}, "branches": [{"branch_id": "B01", "name": "Main", "is_default": true}, {"branch_id": "B02", "name": "Kings Branch 1", "is_default": false}], "isActive": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["System Portal > Tenants"], "summary": "Delete tenant", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1001"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/get-tenants": {"get": {"tags": ["System Portal > Tenants"], "summary": "Tenant list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "deviceToken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/change-tenant-owner-password": {"put": {"tags": ["System Portal > Tenants"], "summary": "Chnage tenant owner password", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"type": "EMAIL_LINK", "tenantId": 1003}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/assigned-tenant": {"post": {"tags": ["System Portal > Assigned Tenant"], "summary": "Add assign tenant", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"userRoleId": "632458773b186400982d99a6", "tenantId": 1035}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["System Portal > Assigned Tenant"], "summary": "Get assigned tenant list with filter", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "userRoleId", "in": "query", "schema": {"type": "string"}, "example": "632455a36651cd00804becb0"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["System Portal > Assigned Tenant"], "summary": "Remove assigned tenants", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "userRoleId", "in": "query", "schema": {"type": "string"}, "example": "63259330856c01ff3a6c25dc"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1056"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/system-users": {"get": {"tags": ["System Portal > System Users"], "summary": "List system users", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "5"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "<PERSON>"}, {"name": "roleId", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["System Portal > System Users"], "summary": "Multiple active/inactive system user", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"users": [], "status": "ACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/dashboard": {"get": {"tags": ["System Portal > Dashboard"], "summary": "Dashboard count", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/dashboardSalesState": {"get": {"tags": ["System Portal > Dashboard"], "summary": "Dashboard sales state", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "branchId", "in": "query", "schema": {"type": "string"}, "example": "634015e7a2a6e700126c563d"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "description": "SALES_PERSON / ALL", "example": "ALL"}, {"name": "durationPeriod", "in": "query", "schema": {"type": "string"}, "description": "DAY / WEEK / MONTH", "example": "WEEK"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/user": {"get": {"tags": ["System Portal > User"], "summary": "Get system user details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "userId", "in": "query", "schema": {"type": "string"}, "example": "6368f0ee3b253000115866a6"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["System Portal > User"], "summary": "Add System portal user", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"firstName": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "lastName": {"type": "string", "example": "Dar<PERSON>"}, "roleId": {"type": "string", "example": "62e8d19e5374737f4c9fa89a"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobileNumber": {"type": "integer", "example": "8141600838"}, "countryCode": {"type": "number", "example": "+91"}, "assignedTenants[]": {"type": "integer", "example": "1056"}, "profilePic": {"type": "string", "format": "binary"}, "isActive": {"type": "boolean", "example": "true"}, "file": {"type": "string", "format": "binary"}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["System Portal > User"], "summary": "Edit System portal user", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"userRoleId": "630ef0c04cb0758d5e45c18f", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "roleId": "62e26ce87619e46092f81833", "email": "<EMAIL>", "mobileNumber": 8690935210, "countryCode": "+91", "isActive": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["System Portal > User"], "summary": "delete user", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "userId", "in": "query", "schema": {"type": "string"}, "example": "62fdc9f691530c0012328b25"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/system-portal/get-tenant-defaults": {"get": {"tags": ["System Portal"], "summary": "Tenant defaults", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/branches": {"get": {"tags": ["Tenant Portal > Branches"], "summary": "Tenant braches", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "WAREHOUSE"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/user": {"get": {"tags": ["Tenant Portal > Tenant Portal User > User"], "summary": "Get tenant user details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "userRoleId", "in": "query", "schema": {"type": "string"}, "example": "6369eafc87526f00125f5f04"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Tenant Portal > Tenant Portal User > User"], "summary": "Add user", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"tenantId": {"type": "integer", "example": "1018"}, "portalType": {"type": "string", "description": "from which portal this api has been called", "example": "TENANT_PORTAL"}, "countryCode": {"type": "number", "example": "+91"}, "mobileNumber": {"type": "integer", "example": "9999999999"}, "firstName": {"type": "string", "example": "Nooh"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "createNewUserRoleId": {"type": "string", "example": "65a764de1088ab00121ee92a"}, "profilePic": {"type": "string", "format": "binary"}, "roleId": {"type": "string", "example": "633c324008805fde7360f461"}, "branchId": {"type": "string", "example": "631600e0b57fa7001218a4f2"}, "isActive": {"type": "boolean", "example": "true"}, "allowPriceChange": {"type": "boolean", "example": "true"}, "allowPostingVoucherReceipts": {"type": "boolean", "example": "true"}, "notifications[]": {"type": "string", "example": "{\"notification_type\":\"ORDER_PREPARING\",\"allow_push_notification\":true,\"allow_sms_notification\":true,\"allow_email_notification\":true}"}, "supervisorId": {"type": "string", "example": "62e275ac0b77a6bbdf16fac8"}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Tenant Portal User > User"], "summary": "Update tenant/branch user", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"userRoleId": "63d7c3ec099872001245c559", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "mobileNumber": 6352968563, "countryCode": "+91", "isActive": true, "portalType": "TENANT_PORTAL", "allowPriceChange": true, "roleId": "650137d9206885ba461dd2ab", "notifications": [{"notification_type": "NEW_ORDER", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_SHIPPED", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_DELIVERED", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_PREPARING", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}], "tenantId": 1131, "newSalesPersonId": "63d7c52d099872001245c695"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Tenant Portal > Tenant Portal User > User"], "summary": "Delete user", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "userRoleId", "in": "query", "schema": {"type": "string"}, "example": "63d7c3ec099872001245c559"}, {"name": "newSalesPersonId", "in": "query", "schema": {"type": "string"}, "example": "64c892d6d2e6f000120b590d"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/users": {"get": {"tags": ["Tenant Portal > Tenant Portal User > Users"], "summary": "Get tenant user list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "PAGINATION"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Tenant Portal User > Users"], "summary": "Update user status", "description": "Api for set user status to active or in active", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "userRoleIds": ["636a160a87526f00125faf42", "63d7c52d099872001245c695", "65a7e5055d04c3001235bba9"], "status": "ACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}`"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/userSetting": {"get": {"tags": ["Tenant Portal > Tenant Portal User > User settings"], "summary": "Get setting configurations", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "userRoleId", "in": "query", "schema": {"type": "string"}, "example": "682b359f8e3cf9b406aab699"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/updateUserSetting": {"post": {"tags": ["Tenant Portal > Tenant Portal User > User settings"], "summary": "Update user settings", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "userRoleId": "682b359f8e3cf9b406aab699", "masterPriceId": "638f0e23dad0a0d56511bdcf", "out_of_stock": {"visible": false, "searchable": false}, "preferredLanguage": "ar", "priceChange": false}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/usersDetails": {"post": {"tags": ["Tenant Portal > Tenant Portal User"], "summary": "Get User Details by Ids", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "ids": ["636906513b25300011587a90", "64c892d6d2e6f000120b590d"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/customer-device-access": {"post": {"tags": ["Tenant Portal > Tenant customer > Customer devices"], "summary": "Add customer device", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"userRoleId": "635f7a64374f70490cf10af2", "deviceId": "13254897NIAGDFnAASFDWA2ASSN", "deviceType": "MOBILE", "deviceOs": "ANDROID"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Tenant Portal > Tenant customer > Customer devices"], "summary": "Delete Customer device", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/customer": {"get": {"tags": ["Tenant Portal > Tenant customer > Customer"], "summary": "Get customer details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "example": "{{deviceaccesstype}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "customerUserRoleIds", "in": "query", "schema": {"type": "string"}, "example": "65263d91e018db00122863c8"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Tenant Portal > Tenant customer > Customer"], "summary": "Delete customer", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "userRoleId", "in": "query", "schema": {"type": "string"}, "example": "642bec1a283085ba09fd953c"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Tenant customer > Customer"], "summary": "Edit customer", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"accountNumber": "<PERSON><PERSON>", "customerName": "<PERSON><PERSON>", "legalName": "Hem Pvt Ltd", "salesPersonId": "6369eafc87526f00125f5f04", "priceListId": "63771cd0ba5634438e6c7c4e", "shippingAddress": "2H65+7WX, Motinagar Society, Paldi, Ahmedabad, Gujarat 380007, India", "shippingCountryId": "62e7d842077fb60012eb35e3", "shippingCityId": "63244af322e3eb003821e8ba", "shippingRegionId": "631ae5355eece300129f92da", "shippingCountryCode": "+91", "shippingMobileNumber": **********, "latitude": 23.**************, "longitude": 72.**************, "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "Foram.<PERSON>@mailinator.com", "preferredLanguage": "en", "customerAppAccess": true, "customerCatalogMode": false, "mobileNumber": **********, "countryCode": "+91", "notifications": [{"notification_type": "NEW_ORDER", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_DELIVERED", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_SHIPPED", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_PREPARING", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}], "deviceAccess": [], "isPaymentEnabled": false, "isActive": true, "tenantId": 1131, "userRoleId": "6836b6626b249f1aad093536"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refeshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Tenant Portal > Tenant customer > Customer"], "summary": "Add Customer", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "accountNumber": "<PERSON><PERSON>", "customerName": "<PERSON><PERSON>", "legalName": "Hem Pvt Ltd", "salesPersonId": "6369eafc87526f00125f5f04", "priceListId": "637ca9c784b9540012d6aca3", "shippingAddress": "2H65+7WX, Motinagar Society, Paldi, Ahmedabad, Gujarat 380007, India", "shippingCountryId": "62e7d842077fb60012eb35e3", "shippingCityId": "63244af322e3eb003821e8ba", "shippingRegionId": "631ae5355eece300129f92da", "shippingCountryCode": "+91", "shippingMobileNumber": "**********", "latitude": "23.**************", "longitude": "72.**************", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "Foram.<PERSON>@mailinator.com", "preferredLanguage": "en", "customerAppAccess": true, "customerCatalogMode": false, "mobileNumber": "**********", "countryCode": "+91", "notifications": [{"notification_type": "NEW_ORDER", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_DELIVERED", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_SHIPPED", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}, {"notification_type": "ORDER_PREPARING", "allow_push_notification": true, "allow_sms_notification": true, "allow_email_notification": true}], "deviceAccess": []}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/customers": {"post": {"tags": ["Tenant Portal > Tenant customer > Customers"], "summary": "Update Customers", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"ids\": [\n        \"65263d91e018db00122863c8\"\n    ],\n    \"updateFields\": {\n        \"external_id\": \"C20311\"\n    },\n    \"tenantId\": {{tenantId}}\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Tenant customer > Customers"], "summary": "Update customers ( app access & status )", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"customers": ["63ae7e9338d5b8aeb560c6c3"], "statusType": "APP_ACCESS", "status": "ACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Tenant customer > Customers"], "summary": "Tenant Customer listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "customerType", "in": "query", "schema": {"type": "string"}, "description": "ALL / NEW / UNASSIGNED / INACTIVE / APP_ACCESS / ACTIVE / APP_REQUEST", "example": "ALL"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "description": "ALL / SEARCH / PAGINATION", "example": "PAGINATION"}, {"name": "withOrderStats", "in": "query", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/verifyCustomer": {"post": {"tags": ["Tenant Portal > Tenant customer"], "summary": "Verify Customer", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"action": "SEND_OTP", "countryCode": "+91", "mobileNumber": "9898989898"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/customerPayment": {"get": {"tags": ["Tenant Portal > Tenant customer"], "summary": "List Customers for payment", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "WITHOUT_PAGINATION"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/customerRewardProgram": {"get": {"tags": ["Tenant Portal > Tenant customer"], "summary": "List Customers for Reward Program", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "as1"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "PENDING"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/customerCommonAddress": {"put": {"tags": ["Tenant Portal > Tenant customer"], "summary": "Update customer common address", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"shipping_address": "testing address", "shipping_country_id": "62e7d842077fb60012eb35e3", "shipping_city_id": "63244af322e3eb003821e8ba", "shipping_region_id": "631ae5355eece300129f92da", "gps_coordinates": {"longitude": 12.1, "latitude": 22.9}, "shipping_country_code": "+91", "shipping_mobile_number": "8460353555"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/searchCustomerByMobileNumber": {"get": {"tags": ["Tenant Portal > Tenant customer"], "summary": "Search customer with mobile number", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "countryCode", "in": "query", "schema": {"type": "string"}, "example": "%2B91"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1004"}, {"name": "mobileNumber", "in": "query", "schema": {"type": "integer"}, "example": "8141600838"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/customer/checkExternalId": {"get": {"tags": ["Tenant Portal > Tenant customer"], "summary": "Check Existing External Id", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "accountNumber", "in": "query", "schema": {"type": "string"}, "example": "WTYU5112"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/customerAppAccess": {"put": {"tags": ["Tenant Portal > Tenant customer"], "summary": "App access request update", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"customerUserRoleIds": ["64105b17df65f90012cb0c1d", "6399c66ac871790012407edd"], "accessInfo": {"appAccess": true, "catalogMode": false}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/supervisors": {"get": {"tags": ["Tenant Portal > Supervisors"], "summary": "Get tenant supervisors", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/salesperson": {"get": {"tags": ["Tenant Portal > Sales person"], "summary": "Get sales person listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/price-list": {"get": {"tags": ["Tenant Portal > Tenant price list"], "summary": "price list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1035"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/allowPayment": {"put": {"tags": ["Tenant Portal > Payment"], "summary": "Update Payment Status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"status": true, "userRoleIds": ["65263d91e018db00122863c8"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/paymentTerms": {"get": {"tags": ["Tenant Portal > Payment Terms"], "summary": "List Payment Terms", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/configurations": {"get": {"tags": ["Tenant Portal > Tenant Configurations"], "summary": "Get Configuration", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "description": "SHIPPING_LABEL / APP_SETTING ", "example": "APP_SETTING"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Tenant Portal > Tenant Configurations"], "summary": "Update Shipping Label", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "tenantLegalName": "Kingsman1 Pvt Ltd", "branches": [{"branchId": "636906513b25300011587a95", "name": "Main", "streetAddress": "Kingsman Street", "region": "631ae5355eece300129f92da", "city": "63244af322e3eb003821e8ba", "mobileNumber": 9080706050, "mobileNumberCountryCode": "+91", "phoneNumber": 9080706050, "phoneNumberCountryCode": "+91"}], "shippingLabelLogo": "hawak-logo-black.svg", "isActive": false}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Tenant Configurations"], "summary": "Update App Settings", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1241, "quantityLabel": 10, "considerNewItem": 30, "priceChange": true, "hideOutOfStockProduct": false, "reduceInventory": false, "customerAppAccess": true, "catalogMode": true, "customerAutoCatalogMode": {"enabled": "true", "duration": "20"}, "paymentVoucherWhatsappNotification": false, "preferredLanguageId": "en", "counter": 100000, "prefix": "", "decimalPoint": 2, "preferredLanguage": "en"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/allowNewValidation": {"get": {"tags": ["Tenant Portal > Tenant Allow Validation"], "summary": "Tenant active customers and users", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "description": "CUSTOMERS / USERS", "example": "USERS"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/columnField": {"get": {"tags": ["Tenant Portal > Datasheet"], "summary": "Get column list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "dataType", "in": "query", "schema": {"type": "string"}, "description": "CUSTOMER / PRODUCT", "example": "CUSTOMER"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/dataSheet": {"post": {"tags": ["Tenant Portal > Datasheet"], "summary": "Export datasheet", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"dataType\": \"PRODUCT\",\n    \"operationType\": \"UPDATE\",\n    \"apiVersion\": 2\n}\n"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Datasheet"], "summary": "Tenant datasheet listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "price"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Tenant Portal > Datasheet"], "summary": "Delete datasheets", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/dataSheet/import/getUploadSignature": {"post": {"tags": ["Tenant Portal > Datasheet"], "summary": "Get upload file signature", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"dataType\": \"PRODUCT\",\n    \"operationType\": \"UPDATE\"\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/dataSheet/import/updateUploadFile": {"post": {"tags": ["Tenant Portal > Datasheet"], "summary": "Update upload excel file", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "dataType": "PRODUCT", "operationType": "UPDATE", "fileName": "product_2025-05-06_1746509578223.xlsx", "updateType": "DETAILS", "isReUpload": false, "originalFileName": "product_May_6.xlsx"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/dataSheet/import/validation": {"post": {"tags": ["Tenant Portal > Datasheet"], "summary": "Import validation", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "fileId": "68199f90088cd4a4f2f99f21", "approveType": "ALL"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/holdReasonTemplateOptions": {"get": {"tags": ["Tenant Portal > Hold Reason > Hold Reason Template Options"], "summary": "Get Hold Reason Template Options List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "3a800c12-1d93-4f21-aa15-e1662ebb1188"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.T2w7xXq87fD2jhValNG1c27ShbcZqh-p-QduITYskycN6KuLO-mKBLIyZYhwGczRgiZ3ZcPG1v8TjhysxL8xNuBQ4oHtYxeW1nhsnuPc288u_VstmG6z0XOHezaIJJ1jcWBgSE0x2ZMUvnESfxJpY-Nme3p0V3ZD832KoLAKpQa2kAxiATKRP4TCqXA-Te6XJyE1nnK_82Ff-Dfukpm2hy6E5k4HgF3K8vqN56QSAszeUZMdkhXe6Rt5ISsg5qPBgPYe8Wx04iQtKsYuGhu57Pun5Yx6p6tyyxKIe48WO0bAVd1mIwqSwOfWKP_ipZDab010T6f4CWXllbGDiruRXQ.gUnSnya9E_C1MimS.A80k9pASM4P5wMStxCO0r_Mbv1eoEWJBMXk37edZKpKlNWPRzHjBVCtMfCnmzwFqNREQIW7fu7QLpjajU5NHPnpJ71nAftGc6n7yQS2UW3w3VbuvjF21EZhJj1ZqlDIpSlqHCXsX7O8utnwJSV7DEm4gy0034Kp23R7BEgMGO9uLR-BIeAgRi0wjTpQsKMdcNJpBOcyYeWMQY2JUjDyf1kTvefVfqWkfO_0gyNf8xUgmshrf7H3DCY6vMcAGvag21b2NzrQ35Qrj4kDraGjQjsWD-ieyvOSHH3s7LEusTaRu8lx-q1IN402BoS_GtywSP046m3fTIOTwg8VDNLkGjPZh8dBB6Aqnsz9lDZ-g70V5YwUCDJzyd-KufLE9Rn7ffZITt8lmALe031dw0E1cR23QX4tddH6VaelolrRFsc2vPkrRQ8ljUM3pgslW2jz1IDVpv4Cs1qpwm-hoComFb51mb9XDQ1Z7kMlmRB3mIfuIENQnbcFIfSPQgSBG8x-wBDzyP_chAFLKYkSMl6sY7D_Sgw3_DLtzGoJD6XvjLy0A0gvQFy9ZVlnL4F4iP3PQqp1jXk4qOUjRnpd2V39Hv4otJFLrkvIpV9zP0Rji-FZrocmY3Y9qP0Fspb3Amx6f2G3wgmNCcqwYulb61-2lwYY5qyOhRgDpuXn0YKX0NvwL0U2L3pF-WiNceT4oK5_zNyb89FKakkG_mIA1jvjKp6d-RQAm-4CsKQ9hqRSGW8YHqZYYjrEL5AexF7ZnhCtS8_coWhZ-rPU8UAf_ytbyBZAXAdFuAOuxkDPJhyoeFTZ8eiGR-7KAnsc2DGWT6LlBI_506g9RvYuF4utEWGU-KB6HKD8XA8hM1Ef9vVKGY-k5DVrm67NbnJ_iFAjXdSDSTAp1DNtt5qwmO1xqGUuE-ZFQ1d2tbDexViwQaKgIriulLG7GgQ-TI9pEWaAw6bITs8sZeex96YMm7zgftALGznyJkvWJ60SIkfibvn8P2kOgOhzubRKfvyDm9okSOUrMBRo7MSk5Y0XCJhKzh3gc6VRmtkYhtPjlXhBOV37uLkxB2PaBbZImwxT-kXKJ2vxbZCZivmSqBeD5aCTrofAAS0yOFqA7gF6Svn9QzrXzidJW8r3XNUAOaDVS0_kq2aPCNuVoJMUX5W2Qf0EqZXAyOi72QHiA2OmOnL1ZCBoiuSmLbUEj2a31W6KDsXGgHdXXOY7JqAuNbPPk88r1fxtjHihJo8YVfL-z58PweJUpwl3w5_Vw7EYItMtoDv8s5Zq1t9GmEqGFOidSXGPkFqE49yWhxnqdh1zAXrlnFMlH56P2YETcZYorL6D1Rh_xuA.695aWnB_bHq5zvbgFDG-kA"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "651abbcf1b4b3b0012c6bacf"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/holdReason": {"post": {"tags": ["Tenant Portal > Hold Reason"], "summary": "Create Hold Reason", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "title": "Hold Reason Template", "secondaryLanguageTitle": "Hold Reason Template", "holdTemplates": [{"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "en", "role": "Sales Person"}, {"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "ar", "role": "Sales Person"}, {"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "en", "role": "Customer"}, {"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "ar", "role": "Customer"}], "releaseTemplates": [{"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "en", "role": "Sales Person"}], "isWhatsappMessageEnabled": true, "isActive": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Hold Reason"], "summary": "Get Hold Reason List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Hold Reason"], "summary": "Update Hold Reason", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"id": "656dfe89738fecf419dff900", "tenantId": 1114, "title": "Hold Reason Template", "secondaryLanguageTitle": "Hold Reason Template", "holdTemplates": [{"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "en", "role": "Sales Person"}, {"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "ar", "role": "Sales Person"}, {"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "en", "role": "Customer"}, {"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "ar", "role": "Customer"}], "releaseTemplates": [{"templateId": "26905281-c98e-4741-8976-35afef5baee4", "title": "Order Release - By Customer - English", "parameters": [{"system_key": "legal_name", "integration_key": "legal_name"}], "language": "en", "role": "Sales Person"}], "isWhatsappMessageEnabled": true, "isActive": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/holdReason/template": {"get": {"tags": ["Tenant Portal > Hold Reason"], "summary": "Get Template Details From The Integration", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "templateId", "in": "query", "schema": {"type": "string"}, "example": "26905281-c98e-4741-8976-35afef5baee4"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/holdReason/templates": {"get": {"tags": ["Tenant Portal > Hold Reason"], "summary": "Get Template List By Package", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/holdReason/addHoldReasonToAllTenant": {"post": {"tags": ["Tenant Portal > Hold Reason"], "summary": "Create Hold Reason To All Tenant", "requestBody": {"content": {}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/holdReason/reasonTemplateWithDetails": {"get": {"tags": ["Tenant Portal > Hold Reason"], "summary": "Get Template list with details to hold reason and release reason", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "holdReasonId", "in": "query", "schema": {"type": "string"}, "example": "658bf22c096cd031d1124abe"}, {"name": "type", "in": "query", "schema": {"type": "string"}, "example": "hold_templates"}, {"name": "orderId", "in": "query", "schema": {"type": "string"}, "example": "667c244c6be9309e27742304"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/accountBalance": {"get": {"tags": ["Tenant Portal > Statement"], "summary": "Account <PERSON><PERSON>", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "customerExternalId", "in": "query", "schema": {"type": "string"}, "example": "C20009"}, {"name": "agingDate", "in": "query", "schema": {"type": "integer"}, "description": "yyyyMMdd", "example": "********"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1241"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/accountStatements": {"get": {"tags": ["Tenant Portal > Statement"], "summary": "Account Statements", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "customerExternalId", "in": "query", "schema": {"type": "string"}, "example": "C20311"}, {"name": "fromDate", "in": "query", "schema": {"type": "integer"}, "example": "********"}, {"name": "toDate", "in": "query", "schema": {"type": "integer"}, "example": "********"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Tenant Portal > Statement"], "summary": "Generate Account Statements", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"customerExternalId": "C20311", "agingDate": "********", "fromDate": "********", "toDate": "********", "tenantId": 1241, "timezone": "Asia/Mumbai", "agingSecondaryTitle": "Aging Report", "statementSecondaryTitle": "Statement of Account"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/calculator/sap": {"post": {"tags": ["Tenant Portal > Reward Program > Calculator"], "summary": "SAP Points", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"paymentAmount": 3000, "aging": {"balance_0_30_days": 0, "balance_31_60_days": 0, "balance_61_90_days": 0, "balance_91_120_days": 2000, "balance_120_more_days": 1000}, "reward": {"baseAmount": 100, "payment": {"0_30_days": 80, "31_60_days": 50, "61_90_days": 30, "91_120_days": 10, "120_more_days": 10}}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Reward Program > Calculator"], "summary": "Check Points Thought SAP", "parameters": [{"name": "customerExternalId", "in": "query", "schema": {"type": "string"}, "example": "C20311"}, {"name": "date", "in": "query", "schema": {"type": "integer"}, "description": "YYYYMMDD", "example": "20240125"}, {"name": "membership", "in": "query", "schema": {"type": "string"}, "description": "CLASSIC,VIP", "example": "CLASSIC"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1241"}, {"name": "configurations.base_url", "in": "query", "schema": {"type": "string"}, "example": "https://www.google.com/"}, {"name": "configurations.username", "in": "query", "schema": {"type": "string"}, "example": "admin"}, {"name": "configurations.password", "in": "query", "schema": {"type": "integer"}, "example": "12345"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/": {"post": {"tags": ["Tenant Portal > Reward Program > Program"], "summary": "Create Reward Program", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"isActive\": true,\n    \"name\": \"Reward Program\",\n    \"secondaryLanguageName\": \"Reward Program\",\n    \"milestones\": [\n        {\n            \"coinsRequired\": 1000,\n            \"bonusCoins\": 100\n        },\n        {\n            \"coinsRequired\": 2000,\n            \"bonusCoins\": 200\n        },\n        {\n            \"coinsRequired\": 3000,\n            \"bonusCoins\": 500\n        }\n    ],\n    \"vipRules\": {\n        \"upgradePoints\": 200,\n        \"renewPoints\": 150\n    },\n    \"baseAmount\": \"100\",\n    \"classicMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 20,\n            \"cancellation\": -20\n        },\n        \"payment\": {\n            \"0_30_days\": 80,\n            \"31_60_days\": 50,\n            \"61_90_days\": 30,\n            \"91_120_days\": 10,\n            \"120_days_above\": 5\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 24,\n            \"cancellation\": -24\n        },\n        \"payment\": {\n            \"0_30_days\": 96,\n            \"31_60_days\": 60,\n            \"61_90_days\": 36,\n            \"91_120_days\": 12,\n            \"120_days_above\": 6\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipPointsRules\": {\n        \"payment\": {\n            \"0_30_days\": 5,\n            \"31_60_days\": 4,\n            \"61_90_days\": 2,\n            \"91_120_days\": 1,\n            \"120_days_above\": 1\n        }\n    }\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Reward Program > Program"], "summary": "Update Reward Program", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"rewardProgramId": "65c0c245664bec3b14b9e5b3", "isActive": false, "name": "Reward Programs", "secondaryLanguageName": "Reward Program", "milestones": [{"coinsRequired": 1000, "bouncePoints": 100}, {"coinsRequired": 2000, "bouncePoints": 200}, {"coinsRequired": 3000, "bouncePoints": 500}], "vipRules": {"upgradePoints": 200, "renewPoints": 150}, "baseAmount": "100", "classicMemberCoinRules": {"invoice": {"normal": 20, "cancellation": -20}, "payment": {"0_30_days": 80, "31_60_days": 50, "61_90_days": 30, "91_120_days": 10, "120_days_above": 5}, "creditNotes": {"returns": -50, "discounts": -20}, "actionTypes": {"dailyAccess": 20, "memberScan": {"scanDurationLimit": 20, "coins": 20}}}, "vipMemberCoinRules": {"invoice": {"normal": 24, "cancellation": -24}, "payment": {"0_30_days": 96, "31_60_days": 60, "61_90_days": 36, "91_120_days": 12, "120_days_above": 6}, "creditNotes": {"returns": -50, "discounts": -20}, "actionTypes": {"dailyAccess": 20, "memberScan": {"scanDurationLimit": 20, "coins": 20}}}, "vipPointsRules": {"payment": {"0_30_days": 5, "31_60_days": 4, "61_90_days": 2, "91_120_days": 1, "120_days_above": 1}}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Reward Program > Program"], "summary": "List Reward Programs", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/configuration": {"put": {"tags": ["Tenant Portal > Reward Program > Configuration"], "summary": "Update Reward Program Configuration", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"baseAmount\": \"100\",\n    \"classicMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 20,\n            \"cancellation\": -20\n        },\n        \"payment\": {\n            \"0_30_days\": 80,\n            \"31_60_days\": 50,\n            \"61_90_days\": 30,\n            \"91_120_days\": 10,\n            \"120_days_above\": 5\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipMemberCoinRules\": {\n        \"invoice\": {\n            \"normal\": 24,\n            \"cancellation\": -24\n        },\n        \"payment\": {\n            \"0_30_days\": 96,\n            \"31_60_days\": 60,\n            \"61_90_days\": 36,\n            \"91_120_days\": 12,\n            \"120_days_above\": 6\n        },\n        \"creditNotes\": {\n            \"returns\": -50,\n            \"discounts\": -20\n        },\n        \"actionTypes\": {\n            \"dailyAccess\": 20,\n            \"memberScan\": {\n                \"scanDurationLimit\": 20,\n                \"coins\": 20\n            }\n        }\n    },\n    \"vipPointsRules\": {\n        \"payment\": {\n            \"0_30_days\": 5,\n            \"31_60_days\": 4,\n            \"61_90_days\": 2,\n            \"91_120_days\": 1,\n            \"120_days_above\": 1\n        }\n    }\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Reward Program > Configuration"], "summary": "Get Reward Program Configuration", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/members": {"post": {"tags": ["Tenant Portal > Reward Program > Members"], "summary": "Enrol Members In Reward Program", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"customerUserRoleIds\": [\n       \"65263d91e018db00122863c8\",\n       \"655b12671eaff80012db433f\"\n    ],\n    \"effectiveDate\": \"2024/12/30\",\n    \"timzone\": \"Asia/Mumbai\",\n    \"customerPaymentTermInfo\": {\n        \"id\": \"65bb3005681802b2e9ca811e\",\n        \"numberOfDays\": 60\n    },\n    \"rewardProgramId\": \"6645e04d94fc9a0011a62592\"\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Reward Program > Members"], "summary": "Update Members Details of Reward Program", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"customerUserRoleIds\": [\n        \"65263d91e018db00122863c8\"\n    ],\n    \"customerPaymentTermId\": \"65bb3005681802b2e9ca811f\",\n    \"rewardProgramId\": \"65b9f9b4dc667a1a3c23abed\",\n    \"restorePointsLevel\": true\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Reward Program > Members"], "summary": "List Members of Reward Program", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Tenant Portal > Reward Program > Members"], "summary": "Remove Members From Reward Program", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "rewardProgramId", "in": "query", "schema": {"type": "string"}, "example": "65dec0ab329fb70d7a34bdce"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/member": {"get": {"tags": ["Tenant Portal > Reward Program > Members"], "summary": "Members Details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "customerUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "655b12671eaff80012db433f"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/memberCount": {"get": {"tags": ["Tenant Portal > Reward Program > Members"], "summary": "Members Count", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "rewardProgramId", "in": "query", "schema": {"type": "string"}, "example": "65e6ac5df5f0641b54d9e47a"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/point": {"get": {"tags": ["Tenant Portal > Reward Program > Points"], "summary": "List Point Logs", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "rewardProgramId", "in": "query", "schema": {"type": "string"}, "example": "65c0c245664bec3b14b9e5b3"}, {"name": "rewardProgramMemberId", "in": "query", "schema": {"type": "string"}, "example": "65c5aab4bc91db08ae8f32ec"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "usedForMobile", "in": "query", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Tenant Portal > Reward Program > Points"], "summary": "Add Manual points", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "customerUserRoleId": "66a22a69284277001262960c", "rewardProgramMemberId": "680728aff9127bb5f3385cd0", "rewardProgramId": "68060b45f9127bb5f3382ace", "points": 5026, "pointType": "COINS", "logType": "PAYMENT", "description": "Manual PAYMENT Notification Test - 1 points"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/dailyAccess": {"post": {"tags": ["Tenant Portal > Reward Program > Points"], "summary": "Daily Access", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"customerUserRoleId\" : \"65263d91e018db00122863c8\",\n    \"timezone\": \"Asia/Kolkata\"\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/memberScan": {"get": {"tags": ["Tenant Portal > Reward Program > Points"], "summary": "Get QR Code For Member <PERSON>an", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "customerUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "655c232cc140170012f656de"}, {"name": "rewardProgramId", "in": "query", "schema": {"type": "string"}, "example": "67b57b7621ba215ce2f1b8a3"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/product": {"post": {"tags": ["Tenant Portal > Reward Program > Product"], "summary": "Create Reward Product", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"name\": \"Reward 1\",\n    \"secondaryLanguageName\": \"Reward 1\",\n    \"productVariantId\": \"645501bb6abbc8e1140b0c0d\",\n    \"itemNumber\": \"rw_1\",\n    \"requiredCoins\": 1,\n    \"inventory\": 10,\n    \"tags\": [\n        \"Garage\"\n    ],\n    \"type\": \"FROM_PRODUCT\",\n    \"isFeatured\": true,\n    \"isActive\": true\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Reward Program > Product"], "summary": "Update Reward Product", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"name\": \"Reward 1\",\n    \"secondaryLanguageName\": \"Reward 1\",\n    \"productVariantId\": \"645501bb6abbc8e1140b0c0d\",\n    \"itemNumber\": \"rw_1\",\n    \"requiredCoins\": 1,\n    \"inventory\": 10,\n    \"tags\": [\n        \"Garage\"\n    ],\n    \"type\": \"FROM_PRODUCT\",\n    \"isFeatured\": true,\n    \"isActive\": true\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Reward Program > Product"], "summary": "List Reward Product", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "patch": {"tags": ["Tenant Portal > Reward Program > Product"], "summary": "Update Reward Products", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"isActive\": true,\n    \"rewardProductIds\": [\n        \"6603e11cad31814ffc6730a5\"\n    ]\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Tenant Portal > Reward Program > Product"], "summary": "Delete Reward Products", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/productTags": {"get": {"tags": ["Tenant Portal > Reward Program > Product"], "summary": "List Reward Product Tags", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "car"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/productClaims": {"post": {"tags": ["Tenant Portal > Reward Program > Product Claims"], "summary": "C<PERSON>m Reward Product", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1241, "rewardProductId": "67c046f23d1970d1bfcd60cd", "rewardProgramId": "67b40beff475195453ac99f3", "rewardProgramMemberId": "67bbfba65ffc8ff62b1d1767", "customerUserRoleId": "655b12671eaff80012db433f"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Reward Program > Product Claims"], "summary": "Update Claimed Reward Product Status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"status\": \"PENDING\",\n    \"claimIds\": [\n        \"65deeff0ece2e531aaf9309f\"\n    ]\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Tenant Portal > Reward Program > Product Claims"], "summary": "List Reward Product Claims", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "claimStatus", "in": "query", "schema": {"type": "string"}, "example": "PROCESSING"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/pointsSummary": {"get": {"tags": ["Tenant Portal > Reward Program > Dashboard"], "summary": "Dashboard Statistics", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "year", "in": "query", "schema": {"type": "integer"}, "example": "2024"}, {"name": "pointType", "in": "query", "schema": {"type": "string"}, "example": "COINS"}, {"name": "timezone", "in": "query", "schema": {"type": "string"}, "example": "Asia/Kolkata"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/rewardProgram/pointsHistoricDistribution": {"get": {"tags": ["Tenant Portal > Reward Program > Dashboard"], "summary": "Dashboard Historic Distribution", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "rewardProgramId", "in": "query", "schema": {"type": "string"}, "example": "65dec0ab329fb70d7a34bdce"}, {"name": "pointType", "in": "query", "schema": {"type": "string"}, "example": "COINS"}, {"name": "durationPeriod", "in": "query", "schema": {"type": "string"}, "example": "MONTH"}, {"name": "timezone", "in": "query", "schema": {"type": "string"}, "example": "Asia/Kolkata"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/schedule-notification": {"post": {"tags": ["Tenant Portal > Notification > Schedule"], "summary": "Schedule Notification", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"title\": \"Deals\",\n    \"message\": \"New deals commming\",\n    \"scheduleAt\": \"2022-09-09T00:00:00\",\n    \"timezone\": \"Asia/Kolkata\",\n    \"data\": {\n        \"tenantId\": 1241,\n        \"dealId\": \"6630889f9958d544b8ab14c5\"\n    },\n    \"notificationType\": \"DEALS_UPDATES\",\n    \"priceListIds\": [\n        \"65263d3ed5e2ed3184c7b894\"\n    ]\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Tenant Portal > Notification > Schedule"], "summary": "Update Schedule Notification", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"scheduledNotificationId\": \"6641a7cff0c2552f8f6dcd8c\",\n    \"tenantId\": {{tenantId}},\n    \"title\": \"Deals\",\n    \"message\": \"New deals commming\",\n    \"scheduleAt\": \"2022-09-07T00:00:00\",\n    \"timezone\": \"Asia/Kolkata\"\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/notifications/": {"get": {"tags": ["Tenant Portal > Notification"], "summary": "List Notifications", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "50"}, {"name": "userRoleId", "in": "query", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "patch": {"tags": ["Tenant Portal > Notification"], "summary": "Unread notification count", "requestBody": {"content": {}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "userRoleId", "in": "query", "schema": {"type": "string"}, "example": "65263a21e018db0012285692"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/scanQrCode": {"post": {"tags": ["Tenant Portal"], "summary": "Scan QR Code", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"customerUserRoleId\": \"65263d91e018db00122863c8\",\n    \"qrInfo\": \"3kwVQ//ulKXY8KG3:4IpM2UlM9pa+RAs7DWcfpkvAcN6D5lGT9R+vPOdvh0lD790Ol9oB7ngwJeUrbmBaKwlL7jTdM7eTC7fQhKGABHnem0pYUljAuAKvPgQF1ERUsG1165wfCa0Z4BksQInCnb0rw8r67srntzaqfzs/og==:DJItfosXDRf5qTlLd66M7Q==\",\n    \"timezone\": \"Asia/Kolkata\"\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tenant-portal/eachOtherLinkedTenants": {"get": {"tags": ["Tenant Portal"], "summary": "List of each-other linked tenants", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}}}