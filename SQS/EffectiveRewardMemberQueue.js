const AWS = require("aws-sdk");
const { Consumer } = require("sqs-consumer");

const { INTEGRATION_CHANNELS, ENTITY_STATUS } = require("../Configs/constants");

const RewardProgramPointController = new (require("../Controllers/RewardProgram/RewardProgramPointController"))()

const IntegrationCredentialModel = new (require("../Models/IntegrationCredentialModel"))();
const RewardProgramMemberModel = new (require("../Models/RewardProgram/RewardProgramMemberModel"))();
const RewardProgramModel = new (require("../Models/RewardProgram/RewardProgramModel"))();
const RewardProgramMemberController = new (require("../Controllers/RewardProgram/RewardProgramMemberController"))()

const sqs = new AWS.SQS(
    {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
    }
);

const getLastFourFifthDates = (
    fromDate,
    toDate
) => {
    const date = moment(fromDate)
    const fifthDates = []

    if (date.date() > 5) {
        date.add(1, "month").date(5);
    } else {
        date.date(5);
    }

    while (date.isSameOrBefore(toDate, "day")) {
        fifthDates.push(date.format("YYYY/MM/DD"))
        date.add(1, "month").date(5)
    }

    return fifthDates.slice(-4)
}

const handleMessage = async (event) => {
    try {
        const {
            customerUserRoleId,
            rewardProgramId,
            tenantId,
            timezone,
            effectiveDate
        } = event

        const fromDate = moment.tz(effectiveDate, "YYYY/MM/DD", timezone)
        const toDate = moment.tz(timezone).subtract(1, "day")

        const membershipDates = getLastFourFifthDates(
            fromDate,
            toDate
        )

        const memberFilter = {
            customerUserRoleId,
            rewardProgramId,
            tenantId,
            status: ENTITY_STATUS.ACTIVE
        }

        const [
            rewardProgram,
            sapIntegrationCredentials,
        ] = await Promise.all([
            RewardProgramModel.findRewardProgramById(rewardProgramId),
            IntegrationCredentialModel.getCredential(
                {
                    name: INTEGRATION_CHANNELS.SAP_SERVICE,
                    is_active: true,
                    tenant_id: tenantId,
                    "configurations.is_reward_program_enabled": true
                },
                "tenant_id configurations",
                {
                    lean: true
                }
            )
        ])

        if (!sapIntegrationCredentials) {
            return
        }

        await RewardProgramMemberModel.membersCallBackWithPagination(memberFilter, async (memberInfo) => {
            await RewardProgramPointController.addPointsByRewardMember(
                parseInt(fromDate.format("YYYYMMDD")),
                parseInt(toDate.format("YYYYMMDD")),
                rewardProgram,
                sapIntegrationCredentials,
                timezone
            )(memberInfo)

            for (let i = 0; i < membershipDates.length; i++) {
                const date = membershipDates[i]

                await RewardProgramMemberController.updateMembershipByRewardMember(
                    rewardProgram,
                    sapIntegrationCredentials,
                    date
                )(memberInfo)
            }
        })
    }
    catch (error) {
        logger.error(error)
    }
}

const initializeEffectiveRewardMemberQueue = () => {
    const consumer = Consumer.create({
        queueUrl: process.env.EFFECTIVE_REWARD_MEMBER_QUEUE_URL,
        sqs,
        handleMessageBatch: async (messages) => {
            if (messages.length) {
                const tasks = messages.map(message => {
                    const event = JSON.parse(message.Body)
                    return handleMessage(event)
                })

                const results = await Promise.allSettled(tasks)
                results.forEach(result => {
                    if (result.reason) {
                        logger.error(result.reason)
                    }
                })
            }
        },
        batchSize: 1,
    })

    consumer.on("error", (err) => {
        logger.error(err)
    });

    consumer.on("processing_error", (err) => {
        logger.error(err)
    });

    consumer.on("timeout_error", (err) => {
        logger.error(err)
    });

    consumer.start();
}

module.exports = {
    initializeEffectiveRewardMemberQueue,
};
