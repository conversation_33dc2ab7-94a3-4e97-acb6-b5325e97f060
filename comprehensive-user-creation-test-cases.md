# Comprehensive User Creation Test Cases - Hawak Backend System

## System Analysis Summary

### Identified Validation Rules
1. **Phone Number**: Global uniqueness for system/tenant users, per-tenant for customers
2. **Email**: Global case-insensitive uniqueness for system/tenant users, optional for customers  
3. **Role-Portal Compatibility**: Hard-coded restrictions and portal type matching
4. **Tenant Validation**: Minimum ID 1000, must exist and not be deleted

### Error Messages Identified
- `validation_exists_mobile_number` (409)
- `validation_exists_email` (409) 
- `validation_invalid_role_type` (400)
- `validation_not_found_tenant` (404)
- `mobile_number_already_exists` (409)
- `external_id_already_exists` (409)

---

## Table 1: Portal-Role Compatibility Test Cases

| Test ID | Test Case Name | Portal Type | Role Type | Tenant ID | Expected Result | HTTP Status | Error Message | Priority |
|---------|----------------|-------------|-----------|-----------|-----------------|-------------|---------------|----------|
| UC-001 | Valid System Portal - Super Admin | SYSTEM_PORTAL | Super Admin | N/A | ✅ Success | 200 | - | High |
| UC-002 | Valid System Portal - Account Manager | SYSTEM_PORTAL | Account Manager | N/A | ✅ Success | 200 | - | High |
| UC-003 | Invalid System Portal - System Owner | SYSTEM_PORTAL | System Owner | N/A | ❌ Failure | 400 | validation_invalid_role_type | High |
| UC-004 | Invalid System Portal - Tenant Role | SYSTEM_PORTAL | Admin | N/A | ❌ Failure | 400 | validation_invalid_role_type | High |
| UC-005 | Valid Tenant Portal - Admin | TENANT_PORTAL | Admin | 1001 | ✅ Success | 200 | - | High |
| UC-006 | Valid Tenant Portal - Sales Person | TENANT_PORTAL | Sales Person | 1001 | ✅ Success | 200 | - | High |
| UC-007 | Valid Tenant Portal - Branch Manager | TENANT_PORTAL | Branch Manager | 1001 | ✅ Success | 200 | - | High |
| UC-008 | Valid Tenant Portal - Supervisor | TENANT_PORTAL | Supervisor | 1001 | ✅ Success | 200 | - | High |
| UC-009 | Valid Tenant Portal - Warehouse Clerk | TENANT_PORTAL | Warehouse Clerk | 1001 | ✅ Success | 200 | - | High |
| UC-010 | Valid Tenant Portal - Accountant | TENANT_PORTAL | Accountant | 1001 | ✅ Success | 200 | - | High |
| UC-011 | Valid Tenant Portal - Contributor | TENANT_PORTAL | Contributor | 1001 | ✅ Success | 200 | - | High |
| UC-012 | Invalid Tenant Portal - Tenant Owner | TENANT_PORTAL | Tenant Owner | 1001 | ❌ Failure | 400 | validation_invalid_role_type | High |
| UC-013 | Invalid Tenant Portal - System Role | TENANT_PORTAL | Super Admin | 1001 | ❌ Failure | 400 | validation_invalid_role_type | High |
| UC-014 | Valid Customer Creation | TENANT_PORTAL | Customer | 1001 | ✅ Success | 200 | - | High |
| UC-015 | Invalid Branch Portal - Non-Sales Role | BRANCH_PORTAL | Admin | 1001 | ❌ Failure | 400 | validation_invalid_role_type | Medium |
| UC-016 | Valid Branch Portal - Sales Person | BRANCH_PORTAL | Sales Person | 1001 | ✅ Success | 200 | - | Medium |

---

## Table 2: Phone Number Uniqueness Test Cases

| Test ID | Test Case Name | Portal Type | Phone Number | Country Code | Tenant ID | Preconditions | Expected Result | HTTP Status | Error Message | Priority |
|---------|----------------|-------------|--------------|--------------|-----------|---------------|-----------------|-------------|---------------|----------|
| UC-017 | Unique Phone - System User | SYSTEM_PORTAL | 1234567890 | +1 | N/A | No existing users | ✅ Success | 200 | - | High |
| UC-018 | Unique Phone - Tenant User | TENANT_PORTAL | 1234567891 | +1 | 1001 | No existing users | ✅ Success | 200 | - | High |
| UC-019 | Unique Phone - Customer | TENANT_PORTAL | 1234567892 | +1 | 1001 | No existing customers | ✅ Success | 200 | - | High |
| UC-020 | Duplicate Phone - Same System Users | SYSTEM_PORTAL | 1234567890 | +1 | N/A | System user exists with same phone | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-021 | Duplicate Phone - System + Tenant | TENANT_PORTAL | 1234567890 | +1 | 1001 | System user exists with same phone | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-022 | Duplicate Phone - Same Tenant Users | TENANT_PORTAL | 1234567891 | +1 | 1001 | Tenant user exists with same phone | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-023 | Duplicate Phone - Same Tenant Customers | TENANT_PORTAL | 1234567892 | +1 | 1001 | Customer exists with same phone in tenant | ❌ Failure | 409 | mobile_number_already_exists | High |
| UC-024 | Same Phone - Different Tenants (Users) | TENANT_PORTAL | 1234567893 | +1 | 1002 | User exists with same phone in tenant 1001 | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-025 | Same Phone - Different Tenants (Customers) | TENANT_PORTAL | 1234567894 | +1 | 1002 | Customer exists with same phone in tenant 1001 | ✅ Success | 200 | - | Medium |
| UC-026 | Same Phone - Different Country Codes | SYSTEM_PORTAL | 1234567890 | +44 | N/A | User exists with ************* | ✅ Success | 200 | - | Medium |
| UC-027 | Invalid Phone Format - Letters | SYSTEM_PORTAL | 123abc456 | +1 | N/A | No existing users | ❌ Failure | 400 | Please provide mobile number | Medium |
| UC-028 | Invalid Country Code Format | SYSTEM_PORTAL | 1234567890 | 1 | N/A | No existing users | ❌ Failure | 400 | Please provide valid country code | Medium |
| UC-029 | Empty Phone Number | SYSTEM_PORTAL | "" | +1 | N/A | No existing users | ❌ Failure | 400 | Please provide mobile number | Medium |
| UC-030 | Very Long Phone Number | SYSTEM_PORTAL | 12345678901234567890 | +1 | N/A | No existing users | ⚠️ Needs verification | 400 | - | Low |

---

## Table 3: Email Uniqueness Test Cases

| Test ID | Test Case Name | Portal Type | Email Address | Tenant ID | Preconditions | Expected Result | HTTP Status | Error Message | Priority |
|---------|----------------|-------------|---------------|-----------|---------------|-----------------|-------------|---------------|----------|
| UC-031 | Unique Email - System User | SYSTEM_PORTAL | <EMAIL> | N/A | No existing users | ✅ Success | 200 | - | High |
| UC-032 | Unique Email - Tenant User | TENANT_PORTAL | <EMAIL> | 1001 | No existing users | ✅ Success | 200 | - | High |
| UC-033 | Unique Email - Customer | TENANT_PORTAL | <EMAIL> | 1001 | No existing users | ✅ Success | 200 | - | High |
| UC-034 | Duplicate Email - Same System Users | SYSTEM_PORTAL | <EMAIL> | N/A | System user exists with same email | ❌ Failure | 409 | validation_exists_email | High |
| UC-035 | Duplicate Email - System + Tenant | TENANT_PORTAL | <EMAIL> | 1001 | System user exists with same email | ❌ Failure | 409 | validation_exists_email | High |
| UC-036 | Duplicate Email - Same Tenant Users | TENANT_PORTAL | <EMAIL> | 1001 | Tenant user exists with same email | ❌ Failure | 409 | validation_exists_email | High |
| UC-037 | Case Sensitivity - Upper/Lower | SYSTEM_PORTAL | <EMAIL> | N/A | User <NAME_EMAIL> | ❌ Failure | 409 | validation_exists_email | High |
| UC-038 | Same Email - Different Tenants (Users) | TENANT_PORTAL | <EMAIL> | 1002 | User exists with same email in tenant 1001 | ❌ Failure | 409 | validation_exists_email | High |
| UC-039 | Same Email - Different Tenants (Customers) | TENANT_PORTAL | <EMAIL> | 1002 | Customer exists with same email in tenant 1001 | ✅ Success | 200 | - | Medium |
| UC-040 | Invalid Email Format | SYSTEM_PORTAL | invalid-email | N/A | No existing users | ❌ Failure | 400 | Please provide email id | Medium |
| UC-041 | Empty Email - System User | SYSTEM_PORTAL | "" | N/A | No existing users | ❌ Failure | 400 | Please provide email id | High |
| UC-042 | Empty Email - Customer | TENANT_PORTAL | "" | 1001 | No existing users | ✅ Success | 200 | - | Medium |
| UC-043 | Email with Special Characters | SYSTEM_PORTAL | <EMAIL> | N/A | No existing users | ✅ Success | 200 | - | Low |
| UC-044 | Very Long Email | SYSTEM_PORTAL | <EMAIL> | N/A | No existing users | ⚠️ Needs verification | 400 | - | Low |

---

## Table 4: Tenant-Specific Test Cases

| Test ID | Test Case Name | Portal Type | Tenant ID | Preconditions | Expected Result | HTTP Status | Error Message | Priority |
|---------|----------------|-------------|-----------|---------------|-----------------|-------------|---------------|----------|
| UC-045 | Valid Tenant ID | TENANT_PORTAL | 1001 | Tenant 1001 exists and active | ✅ Success | 200 | - | High |
| UC-046 | Minimum Valid Tenant ID | TENANT_PORTAL | 1000 | Tenant 1000 exists and active | ✅ Success | 200 | - | High |
| UC-047 | Below Minimum Tenant ID | TENANT_PORTAL | 999 | No tenant with ID 999 | ❌ Failure | 400 | Please send valid tenant id | High |
| UC-048 | Non-existent Tenant ID | TENANT_PORTAL | 9999 | No tenant with ID 9999 | ❌ Failure | 404 | validation_not_found_tenant | High |
| UC-049 | Deleted Tenant ID | TENANT_PORTAL | 1002 | Tenant 1002 exists but is_deleted=true | ❌ Failure | 404 | validation_not_found_tenant | High |
| UC-050 | Null Tenant ID - System User | SYSTEM_PORTAL | null | Valid system user creation | ✅ Success | 200 | - | High |
| UC-051 | Null Tenant ID - Tenant User | TENANT_PORTAL | null | Attempting tenant user creation | ❌ Failure | 400 | Please send valid tenant id | High |
| UC-052 | String Tenant ID | TENANT_PORTAL | "1001" | Tenant 1001 exists | ✅ Success | 200 | - | Medium |
| UC-053 | Invalid Tenant ID Format | TENANT_PORTAL | "abc" | Invalid format | ❌ Failure | 400 | Please send valid tenant id | Medium |
| UC-054 | Negative Tenant ID | TENANT_PORTAL | -1 | Invalid negative ID | ❌ Failure | 400 | Please send valid tenant id | Medium |

---

## Table 5: Combined Conflict Scenarios (Full Permutations)

| Test ID | Test Case Name | Phone | Email | Tenant | Portal | Role | Preconditions | Expected Result | HTTP Status | Error Message | Priority |
|---------|----------------|-------|-------|--------|--------|------|---------------|-----------------|-------------|---------------|----------|
| UC-055 | All Unique - System User | +11111111111 | <EMAIL> | N/A | SYSTEM_PORTAL | Super Admin | No conflicts | ✅ Success | 200 | - | High |
| UC-056 | All Unique - Tenant User | +11111111112 | <EMAIL> | 1001 | TENANT_PORTAL | Admin | No conflicts | ✅ Success | 200 | - | High |
| UC-057 | All Unique - Customer | +11111111113 | <EMAIL> | 1001 | TENANT_PORTAL | Customer | No conflicts | ✅ Success | 200 | - | High |
| UC-058 | Same Phone+Email, Same Tenant | +11111111111 | <EMAIL> | 1001 | TENANT_PORTAL | Admin | User exists with same phone+email | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-059 | Same Phone, Diff Email, Same Tenant | +11111111111 | <EMAIL> | 1001 | TENANT_PORTAL | Admin | User exists with same phone | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-060 | Diff Phone, Same Email, Same Tenant | +11111111114 | <EMAIL> | 1001 | TENANT_PORTAL | Admin | User exists with same email | ❌ Failure | 409 | validation_exists_email | High |
| UC-061 | Same Phone+Email, Diff Tenant (Users) | +11111111115 | <EMAIL> | 1002 | TENANT_PORTAL | Admin | User exists in tenant 1001 | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-062 | Same Phone+Email, Diff Tenant (Customers) | +11111111116 | <EMAIL> | 1002 | TENANT_PORTAL | Customer | Customer exists in tenant 1001 | ✅ Success | 200 | - | Medium |
| UC-063 | Cross-Portal Phone Conflict | +11111111111 | <EMAIL> | N/A | SYSTEM_PORTAL | Super Admin | Tenant user exists with same phone | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-064 | Cross-Portal Email Conflict | +11111111117 | <EMAIL> | N/A | SYSTEM_PORTAL | Super Admin | Tenant user exists with same email | ❌ Failure | 409 | validation_exists_email | High |
| UC-065 | Customer Phone vs System User | +11111111118 | <EMAIL> | N/A | SYSTEM_PORTAL | Super Admin | Customer exists with same phone | ❌ Failure | 409 | validation_exists_mobile_number | High |
| UC-066 | System User Phone vs Customer | +11111111111 | <EMAIL> | 1001 | TENANT_PORTAL | Customer | System user exists with same phone | ❌ Failure | 409 | mobile_number_already_exists | High |

---

## Table 6: Edge Cases and Boundary Conditions

| Test ID | Test Case Name | Scenario Description | Expected Result | HTTP Status | Error Message | Priority |
|---------|----------------|---------------------|-----------------|-------------|---------------|----------|
| UC-067 | Concurrent User Creation - Same Phone | Two simultaneous requests with identical phone | One succeeds, one fails | 200/409 | validation_exists_mobile_number | High |
| UC-068 | Soft-Deleted User - Same Credentials | Create user with credentials of soft-deleted user | User restored | 200 | restored_user | Medium |
| UC-069 | Special Characters in Phone | Phone: ******-567-890 | ❌ Failure | 400 | Please provide valid country code | Medium |
| UC-070 | International Phone Formats | Phone: +44-20-7946-0958 | ✅ Success | 200 | - | Medium |
| UC-071 | Unicode Characters in Names | firstName: "José", lastName: "Müller" | ✅ Success | 200 | - | Low |
| UC-072 | SQL Injection in Email | email: "'; DROP TABLE users; --" | ✅ Success (sanitized) | 200 | - | Medium |
| UC-073 | XSS in Name Fields | firstName: "<script>alert('xss')</script>" | ✅ Success (sanitized) | 200 | - | Medium |
| UC-074 | Empty Required Fields | firstName: "", lastName: "" | ❌ Failure | 400 | Please provide first name | High |
| UC-075 | Invalid Role ID Format | roleId: "invalid-id" | ❌ Failure | 400 | Please provide role id | High |
| UC-076 | Non-existent Role ID | roleId: "507f1f77bcf86cd799439011" | ❌ Failure | 404 | Role not found | High |
| UC-077 | Inactive Role | roleId: valid but role.is_active = false | ❌ Failure | 400 | validation_invalid_role_type | Medium |
| UC-078 | Deleted Role | roleId: valid but role.is_deleted = true | ❌ Failure | 400 | validation_invalid_role_type | Medium |
| UC-079 | Missing Authorization Header | No auth token provided | ❌ Failure | 401 | Unauthorized | High |
| UC-080 | Invalid Device Token | devicetoken: "invalid-token" | ❌ Failure | 401 | Unauthorized | Medium |

---

## Test Coverage Summary

### Total Test Cases: 80

#### By Category:
- **Portal-Role Compatibility**: 16 test cases (20%)
- **Phone Number Uniqueness**: 14 test cases (17.5%)
- **Email Uniqueness**: 14 test cases (17.5%)
- **Tenant-Specific**: 10 test cases (12.5%)
- **Combined Conflicts**: 12 test cases (15%)
- **Edge Cases**: 14 test cases (17.5%)

#### By Priority:
- **High Priority**: 56 test cases (70%)
- **Medium Priority**: 20 test cases (25%)
- **Low Priority**: 4 test cases (5%)

#### By Expected Result:
- **Success Cases**: 25 test cases (31.25%)
- **Failure Cases**: 53 test cases (66.25%)
- **Needs Verification**: 2 test cases (2.5%)

---

## Validation Logic Analysis

### Current System Behavior

#### Phone Number Validation:
```javascript
// Global check for all users (system + tenant)
const userWithMobile = await UserAuthModal.findUserByMobileNumber(mobileNumber, countryCode);

// Customer-specific check with unique constraint
unique_mobile_number: country_code + mobile_number (unique per tenant)
```

#### Email Validation:
```javascript
// Case-insensitive global check
const userWithEmail = await UserAuthModal.findUserByEmail(email);
// Uses regex: { email: { $regex: email, $options: "i" } }
```

#### Role-Portal Validation:
```javascript
// Hard-coded restrictions
if (roleInfo.name.toLowerCase() === "system owner") {
    return res.handler.badRequest("validation_invalid_role_type");
}
if (roleInfo.portal_type === VALUES.portals.SYSTEM_PORTAL) {
    return res.handler.badRequest("validation_invalid_role_type");
}
```

### Identified Gaps and Issues

1. **Race Conditions**: Concurrent user creation with same credentials may cause issues
2. **Inconsistent Error Messages**: Different endpoints use different error messages for similar validations
3. **Missing Unique Constraints**: Users table lacks compound unique index for (country_code + mobile_number)
4. **Customer Email Uniqueness**: No validation for duplicate customer emails within same tenant
5. **Soft Delete Handling**: Complex logic for restoring soft-deleted users needs comprehensive testing

### Recommendations

1. **Database Constraints**: Add compound unique indexes
2. **Consistent Error Handling**: Standardize error messages across all endpoints
3. **Concurrency Testing**: Implement stress tests for concurrent operations
4. **Input Sanitization**: Enhance validation for security vulnerabilities
5. **Audit Logging**: Add comprehensive logging for all user creation attempts

---

## Test Execution Guidelines

### Prerequisites
1. **Test Environment**: Isolated database with known test data
2. **Test Roles**: Pre-created roles for each portal type
3. **Test Tenants**: Active tenants (1001, 1002) and deleted tenant (1003)
4. **Authentication**: Valid admin tokens for each portal

### Test Data Setup
```sql
-- Test Tenants
INSERT INTO tenants (id, name, is_active, is_deleted) VALUES
(1001, 'Test Tenant 1', true, false),
(1002, 'Test Tenant 2', true, false),
(1003, 'Deleted Tenant', false, true);

-- Test Users for Conflict Scenarios
INSERT INTO users (email, mobile_number, country_code, is_deleted) VALUES
('<EMAIL>', 1234567890, '+1', false),
('<EMAIL>', 1234567891, '+1', true);
```

### Automation Framework
```javascript
// Example test structure
describe('User Creation Test Suite', () => {
  beforeEach(async () => {
    await setupTestData();
  });

  afterEach(async () => {
    await cleanupTestData();
  });

  describe('Portal-Role Compatibility', () => {
    test('UC-001: Valid System Portal - Super Admin', async () => {
      // Test implementation
    });
  });
});
```

### Performance Benchmarks
- **Single User Creation**: < 500ms
- **Concurrent Users (10)**: < 2 seconds
- **Bulk Creation (100)**: < 30 seconds
- **Database Query Time**: < 100ms per validation check

### Security Testing
- **Input Validation**: Test all boundary conditions
- **SQL Injection**: Verify parameterized queries
- **XSS Prevention**: Test script injection in all text fields
- **Authentication**: Verify token validation on all endpoints
