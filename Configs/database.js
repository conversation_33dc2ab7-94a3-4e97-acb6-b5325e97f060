const mongoose = require('mongoose');
const autoIncrement = require("../ThirdParty/MongooseAutoIncrement");

const { VALUES } = require('./constants');

const dbName = process.env.DB_NAME
const autoIndex = VALUES.IS_PROD_ENV ? false : true

//BUILD A CONNECTION
mongoose.connect(process.env.MONGODB_URL, {
    dbName,
    autoIndex
}).then((mongoose) => {
    const message = `"${dbName}" database connected successfully :)\n`
    logger.info(message)
    //require('../Scripts/2025-07-27-addDocumentNumberToOldPointsLogs')();
})
    .catch(err => logger.error(err));

if (VALUES.IS_DEV_ENV) {
    mongoose.set("debug", true);
}

autoIncrement.initialize(mongoose.connection);

module.exports.mongoose = mongoose
module.exports.autoIncrement = autoIncrement;
