const CronJob = require('cron').CronJob;
const AuthModal = new (require('../Models/auth'))();

const NotificationController = new (require('../Controllers/NotificationController'))()
const RewardProgramDashboardController = new (require("../Controllers/RewardProgram/RewardProgramDashboardController"))()
const RewardProgramMemberController = new (require("../Controllers/RewardProgram/RewardProgramMemberController"))()
const RewardProgramPointController = new (require("../Controllers/RewardProgram/RewardProgramPointController"))()

const { VALUES } = require('./constants');
require('./Logger'); // Import logger

/*
CronJob star explanation:
    * * * * * *
    | | | | | |
    | | | | | day of week
    | | | | month
    | | | day of month
    | | hour
    | minute
    second ( optional )
*/

/**
 * @description CRON job to delete documents from user_sessions collection
 */


if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in server only.
    new CronJob({
        cronTime: "0 3 * * *", // Run cron job everyday at 03:00:00
        onTick: AuthModal.removeOldUserSessionDocs,
        onComplete: function () {
            logger.info("COMPLETED: deleting \n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_DEV_ENV && VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only.
    new CronJob({
        cronTime: "*/15 * * * *", // Run cron job after every 15 mins
        onTick: NotificationController.sentScheduledNotification,
        onComplete: function () {
            console.log("\n COMPLETED: Sent scheduled notification \n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only.
    new CronJob({
        cronTime: "0 1 * * *", // Run cron job everyday at 01:00:00
        onTick: RewardProgramPointController.addPointsThroughSAP,
        onComplete: function () {
            logger.info("COMPLETED: Add reward points through SAP \n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only.
    new CronJob({
        cronTime: "0 2 * * *", // Run cron job everyday at 02:00:00
        onTick: RewardProgramPointController.addPointsThroughSAP5days,
        onComplete: function () {
            logger.info("COMPLETED: Add remaining reward points through SAP \n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only.
    new CronJob({
        cronTime: "0 3 * * *", // Run cron job everyday at 03:00:00
        onTick: RewardProgramDashboardController.addPointsReport,
        onComplete: function () {
            logger.info("COMPLETED: Add reward points reports \n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only.
    new CronJob({
        cronTime: "0 3 5 * *", // Run cron job every 5th of month at 03:00:00
        onTick: RewardProgramMemberController.updateCustomersRewardProgramMembership,
        onComplete: function () {
            logger.info("COMPLETED: Update customers reward program membership \n")
        },
        start: true,
        timeZone: "UTC",
    })
}

if (VALUES.IS_APP_RUNNING_ON_SERVER) { // Run cron job in development server only.
    new CronJob({
        cronTime: "0 4 2 * *", // Run cron job every 2th of month at 04:00:00
        onTick: RewardProgramPointController.expireRewardProgramMemberCoins,
        onComplete: function () {
            logger.info("COMPLETED: Expire reward program member coins \n")
        },
        start: true,
        timeZone: "UTC",
    })
}
