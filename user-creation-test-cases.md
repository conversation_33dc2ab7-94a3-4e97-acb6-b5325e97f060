# User Creation Test Cases Specification

## System Analysis Summary

### Portal Types Identified
1. **SYSTEM_PORTAL** - System administration portal
2. **TENANT_PORTAL** - Tenant management portal  
3. **BRANCH_PORTAL** - Branch management portal
4. **SALES_APP** - Sales application
5. **CUSTOMER_APP** - Customer application
6. **SUPERVISOR_APP** - Supervisor application

### Role Types Identified
1. **System Owner** - System portal only
2. **Super Admin** - System portal
3. **Account Manager** - System portal
4. **Tenant Owner** - Tenant portal (restricted)
5. **Admin** - Tenant portal
6. **Branch Manager** - Branch portal
7. **Sales Person** - Sales app
8. **Supervisor** - Supervisor app
9. **Customer** - Customer app
10. **Warehouse Clerk** - Tenant portal
11. **Accountant** - Tenant portal
12. **Contributor** - Tenant portal

### User Creation Endpoints
1. **POST /system-portal/user** - Create system users (Super Admin, Account Manager)
2. **POST /tenant-portal/user** - Create tenant/branch users (Admin, Branch Manager, Sales Person, etc.)
3. **POST /tenant-portal/customer** - Create customers
4. **POST /auth/signup** - General user signup

### Validation Rules Discovered

#### Phone Number Uniqueness
- **System Users (users collection)**: Phone + country code combination checked globally across all users
- **Customers (tenant_customers collection)**: `unique_mobile_number` field (country_code + mobile_number) enforced with unique constraint
- **Cross-tenant**: Phone numbers can be reused across different tenants for different user types

#### Email Uniqueness  
- **System Users**: Email checked globally across all users (case-insensitive regex)
- **Customers**: Email is optional and not enforced as unique
- **Cross-tenant**: Email conflicts checked globally for system users, not enforced for customers

#### Tenant ID Constraints
- **Minimum Value**: 1000 (enforced in validators)
- **System Users**: No tenant_id (null/undefined)
- **Tenant Users**: Must belong to valid, non-deleted tenant
- **Customers**: Must belong to valid, non-deleted tenant

#### Role-Portal Compatibility Rules
- **System Owner**: Cannot be created via API (restricted)
- **Tenant Owner**: Cannot be created via tenant portal API (restricted)
- **System Portal Roles**: Cannot be assigned in tenant portal
- **Portal Type Validation**: Role's portal_type must match the creation endpoint

## Test Cases

### Category 1: Portal-Role Combinations

| Test ID | Test Name | Portal | Role | Expected Result | Priority |
|---------|-----------|--------|------|-----------------|----------|
| UC-001 | Valid System Portal - Super Admin | SYSTEM_PORTAL | Super Admin | Success | High |
| UC-002 | Valid System Portal - Account Manager | SYSTEM_PORTAL | Account Manager | Success | High |
| UC-003 | Invalid System Portal - System Owner | SYSTEM_PORTAL | System Owner | Fail - Restricted role | High |
| UC-004 | Invalid System Portal - Tenant Role | SYSTEM_PORTAL | Admin | Fail - Invalid portal type | High |
| UC-005 | Valid Tenant Portal - Admin | TENANT_PORTAL | Admin | Success | High |
| UC-006 | Valid Tenant Portal - Sales Person | TENANT_PORTAL | Sales Person | Success | High |
| UC-007 | Valid Tenant Portal - Branch Manager | TENANT_PORTAL | Branch Manager | Success | High |
| UC-008 | Invalid Tenant Portal - Tenant Owner | TENANT_PORTAL | Tenant Owner | Fail - Restricted role | High |
| UC-009 | Invalid Tenant Portal - System Role | TENANT_PORTAL | Super Admin | Fail - Invalid portal type | High |
| UC-010 | Valid Customer Creation | TENANT_PORTAL | Customer | Success | High |

### Category 2: Phone Number Uniqueness Scenarios

| Test ID | Test Name | Portal | Phone | Country Code | Tenant ID | Expected Result | Priority |
|---------|-----------|--------|-------|--------------|-----------|-----------------|----------|
| UC-011 | Unique phone - System user | SYSTEM_PORTAL | +1234567890 | +1 | N/A | Success | High |
| UC-012 | Unique phone - Tenant user | TENANT_PORTAL | +1234567891 | +1 | 1001 | Success | High |
| UC-013 | Unique phone - Customer | TENANT_PORTAL | +1234567892 | +1 | 1001 | Success | High |
| UC-014 | Duplicate phone - Same system users | SYSTEM_PORTAL | +1234567890 | +1 | N/A | Fail - Duplicate mobile | High |
| UC-015 | Duplicate phone - System + Tenant user | TENANT_PORTAL | +1234567890 | +1 | 1001 | Fail - Duplicate mobile | High |
| UC-016 | Duplicate phone - Same tenant customers | TENANT_PORTAL | +1234567892 | +1 | 1001 | Fail - Duplicate mobile | High |
| UC-017 | Same phone - Different tenants (customers) | TENANT_PORTAL | +1234567893 | +1 | 1002 | Success | Medium |
| UC-018 | Same phone - Different country codes | SYSTEM_PORTAL | 1234567890 | +44 | N/A | Success | Medium |
| UC-019 | Invalid phone format | SYSTEM_PORTAL | 123abc | +1 | N/A | Fail - Invalid format | Medium |
| UC-020 | Empty phone number | SYSTEM_PORTAL | "" | +1 | N/A | Fail - Required field | Medium |

### Category 3: Email Uniqueness Scenarios

| Test ID | Test Name | Portal | Email | Tenant ID | Expected Result | Priority |
|---------|-----------|--------|-------|-----------|-----------------|----------|
| UC-021 | Unique email - System user | SYSTEM_PORTAL | <EMAIL> | N/A | Success | High |
| UC-022 | Unique email - Tenant user | TENANT_PORTAL | <EMAIL> | 1001 | Success | High |
| UC-023 | Unique email - Customer | TENANT_PORTAL | <EMAIL> | 1001 | Success | High |
| UC-024 | Duplicate email - System users | SYSTEM_PORTAL | <EMAIL> | N/A | Fail - Duplicate email | High |
| UC-025 | Duplicate email - System + Tenant | TENANT_PORTAL | <EMAIL> | 1001 | Fail - Duplicate email | High |
| UC-026 | Case sensitivity - Upper/Lower | SYSTEM_PORTAL | <EMAIL> | N/A | Fail - Case insensitive | High |
| UC-027 | Same email - Different tenants (customers) | TENANT_PORTAL | <EMAIL> | 1002 | Success | Medium |
| UC-028 | Invalid email format | SYSTEM_PORTAL | invalid-email | N/A | Fail - Invalid format | Medium |
| UC-029 | Empty email - System user | SYSTEM_PORTAL | "" | N/A | Fail - Required field | High |
| UC-030 | Empty email - Customer | TENANT_PORTAL | "" | 1001 | Success - Optional | Medium |

### Category 4: Tenant ID Scenarios

| Test ID | Test Name | Portal | Tenant ID | Expected Result | Priority |
|---------|-----------|--------|-----------|-----------------|----------|
| UC-031 | Valid tenant ID | TENANT_PORTAL | 1001 | Success | High |
| UC-032 | Minimum valid tenant ID | TENANT_PORTAL | 1000 | Success | High |
| UC-033 | Below minimum tenant ID | TENANT_PORTAL | 999 | Fail - Invalid tenant ID | High |
| UC-034 | Non-existent tenant ID | TENANT_PORTAL | 9999 | Fail - Tenant not found | High |
| UC-035 | Deleted tenant ID | TENANT_PORTAL | 1002 | Fail - Tenant deleted | High |
| UC-036 | Null tenant ID - System user | SYSTEM_PORTAL | null | Success | High |
| UC-037 | Null tenant ID - Tenant user | TENANT_PORTAL | null | Fail - Required field | High |
| UC-038 | String tenant ID | TENANT_PORTAL | "1001" | Success - Auto convert | Medium |
| UC-039 | Invalid tenant ID format | TENANT_PORTAL | "abc" | Fail - Invalid format | Medium |

### Category 5: Combined Scenarios (Full Permutations)

| Test ID | Test Name | Phone | Email | Tenant | Portal | Role | Expected Result | Priority |
|---------|-----------|-------|-------|--------|--------|------|-----------------|----------|
| UC-040 | All unique - System user | +1111111111 | <EMAIL> | N/A | SYSTEM_PORTAL | Super Admin | Success | High |
| UC-041 | All unique - Tenant user | +1111111112 | <EMAIL> | 1001 | TENANT_PORTAL | Admin | Success | High |
| UC-042 | All unique - Customer | +1111111113 | <EMAIL> | 1001 | TENANT_PORTAL | Customer | Success | High |
| UC-043 | Same phone+email, same tenant | +1111111111 | <EMAIL> | 1001 | TENANT_PORTAL | Admin | Fail - Duplicate user | High |
| UC-044 | Same phone, diff email, same tenant | +1111111111 | <EMAIL> | 1001 | TENANT_PORTAL | Admin | Fail - Duplicate phone | High |
| UC-045 | Diff phone, same email, same tenant | +1111111114 | <EMAIL> | 1001 | TENANT_PORTAL | Admin | Fail - Duplicate email | High |
| UC-046 | Same phone+email, diff tenant | +1111111115 | <EMAIL> | 1002 | TENANT_PORTAL | Customer | Success | Medium |
| UC-047 | Cross-portal phone conflict | +1111111111 | <EMAIL> | N/A | SYSTEM_PORTAL | Super Admin | Fail - Phone exists | High |
| UC-048 | Cross-portal email conflict | +1111111116 | <EMAIL> | N/A | SYSTEM_PORTAL | Super Admin | Fail - Email exists | High |

### Category 6: Edge Cases

| Test ID | Test Name | Scenario | Expected Result | Priority |
|---------|-----------|----------|-----------------|----------|
| UC-049 | Concurrent user creation - same phone | Two simultaneous requests with same phone | One succeeds, one fails | High |
| UC-050 | Soft-deleted user - same credentials | Create user with credentials of soft-deleted user | User restored | Medium |
| UC-051 | Special characters in phone | Phone with special chars (+, -, spaces) | Validation based on regex | Medium |
| UC-052 | International phone formats | Various country codes and formats | Success if valid regex | Medium |
| UC-053 | Maximum length phone number | Very long phone number | Validation failure | Low |
| UC-054 | Maximum length email | Very long email address | Validation failure | Low |
| UC-055 | Unicode characters in names | Names with unicode/emoji | Success or proper handling | Low |
| UC-056 | SQL injection in inputs | Malicious input strings | Proper sanitization | Medium |
| UC-057 | Empty required fields | Missing firstName, lastName | Validation failure | High |
| UC-058 | Invalid role ID format | Non-ObjectId role ID | Validation failure | High |
| UC-059 | Non-existent role ID | Valid ObjectId but non-existent role | Role not found error | High |
| UC-060 | Inactive/deleted role | Role marked as inactive/deleted | Role validation failure | Medium |

## Test Execution Notes

### Current System Behavior Analysis

1. **Phone Number Validation**: 
   - System uses `findUserByMobileNumber(mobileNumber, countryCode)` for global checks
   - Customers use `unique_mobile_number` field with unique constraint
   - Cross-tenant phone reuse is allowed for customers but not for system/tenant users

2. **Email Validation**:
   - Case-insensitive regex search: `{ email: { $regex: email, $options: "i" } }`
   - Global uniqueness enforced for system and tenant users
   - Optional for customers, no uniqueness constraint

3. **Tenant Validation**:
   - Minimum tenant ID: 1000 (enforced by validator)
   - Tenant existence and deletion status checked
   - Auto-increment starting from 1000

4. **Role-Portal Compatibility**:
   - Hard-coded restrictions for System Owner and Tenant Owner roles
   - Portal type matching enforced in role validation

### Recommendations for Validation Improvements

1. **Add Unique Constraints**: Implement compound unique indexes for (country_code + mobile_number) in users collection
2. **Email Uniqueness**: Consider making email unique constraint at database level
3. **Concurrency Handling**: Add proper handling for concurrent user creation scenarios
4. **Audit Trail**: Implement comprehensive logging for user creation attempts
5. **Rate Limiting**: Add rate limiting for user creation endpoints
6. **Input Sanitization**: Enhance input validation and sanitization

### Coverage Summary

- **Total Test Cases**: 60
- **Portal-Role Combinations**: 10 test cases
- **Phone Number Scenarios**: 10 test cases  
- **Email Scenarios**: 10 test cases
- **Tenant ID Scenarios**: 9 test cases
- **Combined Scenarios**: 9 test cases
- **Edge Cases**: 12 test cases

### Test Priority Distribution

- **High Priority**: 42 test cases (70%)
- **Medium Priority**: 15 test cases (25%)
- **Low Priority**: 3 test cases (5%)

## Detailed Test Case Specifications

### UC-001: Valid System Portal - Super Admin
**Preconditions**: Valid system admin session, unique phone/email
**Input Data**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "mobileNumber": "1234567890",
  "countryCode": "+1",
  "roleId": "60f1b2b3c4d5e6f7a8b9c0d1",
  "isActive": true
}
```
**Expected Result**: User created successfully with Super Admin role
**Validation Rules Applied**: Phone uniqueness, email uniqueness, role-portal compatibility
**Current Behavior**: ✅ Working as expected

### UC-014: Duplicate Phone - Same System Users
**Preconditions**: System user with phone +1234567890 already exists
**Input Data**: Same as UC-001 but with existing phone number
**Expected Result**: HTTP 409 Conflict - "validation_exists_mobile_number"
**Validation Rules Applied**: Global phone uniqueness check
**Current Behavior**: ✅ Working as expected

### UC-025: Duplicate Email - System + Tenant
**Preconditions**: System user <NAME_EMAIL> exists
**Input Data**: Tenant user creation with same email
**Expected Result**: HTTP 409 Conflict - "validation_exists_email"
**Validation Rules Applied**: Global email uniqueness check (case-insensitive)
**Current Behavior**: ✅ Working as expected

### UC-043: Same Phone+Email, Same Tenant
**Preconditions**: User exists in tenant 1001 with specific phone/email
**Input Data**: Attempt to create another user in same tenant with identical credentials
**Expected Result**: HTTP 409 Conflict - Duplicate user detection
**Validation Rules Applied**: Phone uniqueness, email uniqueness, tenant-specific checks
**Current Behavior**: ✅ Working as expected

### UC-049: Concurrent User Creation
**Test Scenario**: Simulate two simultaneous API calls with identical phone numbers
**Expected Result**: One request succeeds, other fails with duplicate error
**Implementation**: Use database unique constraints and proper error handling
**Current Behavior**: ⚠️ Needs testing - potential race condition

## Business Rules Matrix

### Portal-Role Compatibility Matrix

| Role | SYSTEM_PORTAL | TENANT_PORTAL | BRANCH_PORTAL | SALES_APP | CUSTOMER_APP | SUPERVISOR_APP |
|------|---------------|---------------|---------------|-----------|--------------|----------------|
| System Owner | ❌ Restricted | ❌ | ❌ | ❌ | ❌ | ❌ |
| Super Admin | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Account Manager | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Tenant Owner | ❌ | ❌ Restricted | ❌ | ❌ | ❌ | ❌ |
| Admin | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Branch Manager | ❌ | ✅ | ✅ | ❌ | ❌ | ❌ |
| Sales Person | ❌ | ✅ | ❌ | ✅ | ❌ | ❌ |
| Supervisor | ❌ | ✅ | ❌ | ❌ | ❌ | ✅ |
| Customer | ❌ | ✅ | ❌ | ❌ | ✅ | ❌ |
| Warehouse Clerk | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Accountant | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Contributor | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |

### Uniqueness Constraints Matrix

| Field | System Users | Tenant Users | Customers | Scope |
|-------|--------------|--------------|-----------|-------|
| Phone + Country Code | ✅ Unique | ✅ Unique | ✅ Unique | Global for users, Per-tenant for customers |
| Email | ✅ Unique | ✅ Unique | ❌ Optional | Global for users |
| External ID | N/A | N/A | ✅ Unique | Per-tenant |
| Customer ID | N/A | N/A | ✅ Unique | Per-tenant |

## Test Data Templates

### System User Template
```json
{
  "firstName": "System",
  "lastName": "Admin",
  "email": "<EMAIL>",
  "mobileNumber": "**********",
  "countryCode": "+1",
  "roleId": "SUPER_ADMIN_ROLE_ID",
  "isActive": true
}
```

### Tenant User Template
```json
{
  "firstName": "Tenant",
  "lastName": "Manager",
  "email": "<EMAIL>",
  "mobileNumber": "**********",
  "countryCode": "+1",
  "roleId": "ADMIN_ROLE_ID",
  "tenantId": 1001,
  "branchId": "BRANCH_ID",
  "isActive": true,
  "notifications": [{"type": "email", "enabled": true}]
}
```

### Customer Template
```json
{
  "firstName": "John",
  "lastName": "Customer",
  "email": "<EMAIL>",
  "mobileNumber": "3000000001",
  "countryCode": "+1",
  "tenantId": 1001,
  "salesPersonId": "SALES_PERSON_ID",
  "latitude": 25.2048,
  "longitude": 55.2708
}
```

## Automation Test Scripts

### Test Case UC-001 (Jest/Supertest)
```javascript
describe('System User Creation', () => {
  test('UC-001: Create Super Admin successfully', async () => {
    const userData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      mobileNumber: '1234567890',
      countryCode: '+1',
      roleId: SUPER_ADMIN_ROLE_ID,
      isActive: true
    };

    const response = await request(app)
      .post('/system-portal/user')
      .set('Authorization', `Bearer ${systemAdminToken}`)
      .set('devicetoken', 'test-device-token')
      .set('userroleid', systemAdminRoleId)
      .send(userData);

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});
```

### Test Case UC-014 (Duplicate Phone)
```javascript
test('UC-014: Reject duplicate phone number', async () => {
  // First create a user
  await createSystemUser({
    email: '<EMAIL>',
    mobileNumber: '1234567890',
    countryCode: '+1'
  });

  // Try to create another with same phone
  const response = await request(app)
    .post('/system-portal/user')
    .set('Authorization', `Bearer ${systemAdminToken}`)
    .send({
      firstName: 'Second',
      lastName: 'User',
      email: '<EMAIL>',
      mobileNumber: '1234567890',
      countryCode: '+1',
      roleId: SUPER_ADMIN_ROLE_ID
    });

  expect(response.status).toBe(409);
  expect(response.body.message).toContain('validation_exists_mobile_number');
});
```

## Performance Test Scenarios

### Load Testing
- **Concurrent User Creation**: 100 simultaneous user creation requests
- **Bulk Import**: Create 1000 users via batch API
- **Database Performance**: Monitor query performance with large user datasets

### Stress Testing
- **Duplicate Detection**: Test duplicate detection with high concurrency
- **Memory Usage**: Monitor memory consumption during bulk operations
- **Database Locks**: Test for deadlocks during concurrent operations

## Security Test Cases

### Input Validation
- **SQL Injection**: Test malicious SQL in all input fields
- **XSS Prevention**: Test script injection in name fields
- **Command Injection**: Test system command injection attempts

### Authentication & Authorization
- **Role Escalation**: Attempt to create users with higher privileges
- **Cross-Tenant Access**: Attempt to create users in unauthorized tenants
- **Token Validation**: Test with expired/invalid authentication tokens

## Regression Test Suite

### Critical Path Tests (Must Pass)
1. UC-001, UC-002: Basic system user creation
2. UC-005, UC-006, UC-007: Basic tenant user creation
3. UC-010: Customer creation
4. UC-014, UC-015: Phone number duplicate detection
5. UC-024, UC-025: Email duplicate detection

### Smoke Tests (Quick Validation)
1. Create one user of each role type
2. Verify basic duplicate detection
3. Test invalid tenant ID rejection
4. Test role-portal compatibility

### Full Regression (Complete Coverage)
- Execute all 60 test cases
- Include performance benchmarks
- Validate error message consistency
- Test API response format compliance
