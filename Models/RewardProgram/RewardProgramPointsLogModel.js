const moment = require("moment")
const RewardProgramPointsLogSchema = require("../../Database/Schemas/RewardProgram/RewardProgramPointsLogSchema")

const RewardProgramMemberModel = new (require("./RewardProgramMemberModel"))()
const RewardProgramMemberExpiryPointsModel = new (require("./RewardProgramMemberExpiryPointsModel"))()
const RewardProgramNotificationModel = new (require("./RewardProgramNotificationModel"))()
const CommonModel = new (require("../common"))()

const {
    SAP_SERVICE,
    REWARD_PROGRAM
} = require('../../Configs/constants');

const { logRewardProgram } = require("../../Utils/logHelper")

module.exports = class {

    addPointsLog = async (
        body,
        memberInfo,
        userId,
        session
    ) => {
        const {
            tenantId,
            rewardProgramId,
            rewardProgramMemberId,
            customerUserRoleId,
            productClaimId,
            points,
            logType,
            calculations,
            entryType,
            pointType,
            logDate,
            amount,
            description,
            documentNumber
        } = body

        const newRewardPointsLog = new RewardProgramPointsLogSchema(
            {
                tenant_id: tenantId,
                customer_user_role_id: customerUserRoleId,
                reward_program_member_id: rewardProgramMemberId,
                reward_program_id: rewardProgramId,
                point_type: pointType,
                entry_type: entryType,
                log_type: logType,
                product_claim_id: productClaimId,
                calculations,
                points,
                amount,
                log_date: logDate,
                description,
                document_number: documentNumber,
                created_by: userId,
                updated_by: userId
            }
        )

        const logPoint = (info) => {
            logRewardProgram(`For ${info.log_type}, ${info.entry_type} ${info.points} ${info.point_type}. Reward memberId: ${info.reward_program_member_id}`, {
                tenantId,
                logDate,
                rewardProgramId,
                rewardProgramMemberId,
                customerUserRoleId,
                productClaimId,
            })
        }

        const callBack = async (session) => {
            switch (pointType) {
                case REWARD_PROGRAM.POINT.TYPE.COINS: {
                    switch (entryType) {
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED: {
                            if (logType === REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.REFUND) {
                                memberInfo.coins.claimed -= points
                            }

                            let updatedCoins = memberInfo.coins.remaining + points

                            for (let i = 1; i <= memberInfo.reward_program_id.milestones.length; i++) {
                                const {
                                    coins_required,
                                    bonus_coins,
                                    _id: milestoneId
                                } = memberInfo.reward_program_id.milestones[i - 1];

                                if (
                                    memberInfo.coins.remaining < coins_required &&
                                    updatedCoins >= coins_required
                                ) {
                                    const pointsLogs = await this.findPointsLogsByFilter(
                                        {
                                            tenant_id: tenantId,
                                            customer_user_role_id: customerUserRoleId,
                                            log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SYSTEM.MILESTONES,
                                            milestone_id: milestoneId,
                                            point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                                            entry_type: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                                        },
                                        undefined,
                                        {
                                            session,
                                            lean: true
                                        }
                                    )

                                    if (!pointsLogs.length) {
                                        const milestonePointLog = new RewardProgramPointsLogSchema({
                                            tenant_id: tenantId,
                                            customer_user_role_id: customerUserRoleId,
                                            reward_program_member_id: rewardProgramMemberId,
                                            reward_program_id: rewardProgramId,
                                            point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                                            entry_type: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                                            log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SYSTEM.MILESTONES,
                                            milestone_id: milestoneId,
                                            points: bonus_coins,
                                            log_date: logDate,
                                            description,
                                            created_by: userId,
                                            updated_by: userId
                                        })

                                        await milestonePointLog.save({ session })
                                        logPoint("milestonePointLog", milestonePointLog)
                                        await RewardProgramNotificationModel.sentRewardPointNotification(milestonePointLog, session, i)

                                        const statisticsInfos = RewardProgramMemberModel.getStatisticsInfoFormPointsLog(milestonePointLog)
                                        statisticsInfos.forEach(statisticsInfo => {
                                            memberInfo[statisticsInfo.pointKey][statisticsInfo.key] += statisticsInfo.points
                                        })

                                        updatedCoins += bonus_coins
                                    }
                                }
                            }

                            const expireDate = moment().add(1, "years")
                            await RewardProgramMemberExpiryPointsModel.upsertExpiryPoints(
                                {
                                    tenant_id: tenantId,
                                    customer_user_role_id: customerUserRoleId,
                                    reward_program_member_id: rewardProgramMemberId,
                                    reward_program_id: rewardProgramId,
                                    point_type: pointType,
                                    expiry_year: parseInt(expireDate.format("YYYY")),
                                    expiry_month: parseInt(expireDate.format("MM")),
                                },
                                {
                                    "$inc": {
                                        points: updatedCoins - memberInfo.coins.remaining
                                    }
                                },
                                {
                                    session
                                }
                            )

                            memberInfo.coins.remaining = updatedCoins

                            break;
                        }
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.CLAIMED: {
                            memberInfo.coins.claimed += points
                            memberInfo.coins.remaining -= points

                            await RewardProgramMemberExpiryPointsModel.destroyExpiryPoints(
                                points,
                                pointType,
                                rewardProgramMemberId,
                                tenantId,
                                session
                            )

                            break;
                        }
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED: {
                            memberInfo.coins.expired += points
                            memberInfo.coins.remaining -= points
                            break;
                        }
                    }
                    break;
                }
                case REWARD_PROGRAM.POINT.TYPE.VIP_POINTS: {
                    switch (entryType) {
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED: {
                            memberInfo.vip_points.remaining += points

                            if (logType === REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.REFUND) {
                                memberInfo.vip_points.claimed -= points
                            }
                            break;
                        }
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.CLAIMED: {
                            memberInfo.vip_points.claimed += points
                            memberInfo.vip_points.remaining -= points
                            break;
                        }
                        case REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED: {
                            memberInfo.vip_points.expired += points
                            memberInfo.vip_points.remaining -= points
                            break;
                        }
                    }
                    break;
                }
            }

            await newRewardPointsLog.save({ session })
            logPoint("newRewardPointsLog", newRewardPointsLog)
            await RewardProgramNotificationModel.sentRewardPointNotification(newRewardPointsLog, session)

            const statisticsInfos = RewardProgramMemberModel.getStatisticsInfoFormPointsLog(newRewardPointsLog)
            statisticsInfos.forEach(statisticsInfo => {
                memberInfo[statisticsInfo.pointKey][statisticsInfo.key] += statisticsInfo.points
            })

           await memberInfo.save({ session })
        }

        if (session) {
            await callBack(session)
        }
        else {
            await CommonModel.transactionCallback(callBack)
        }

        return newRewardPointsLog
    }

    findPointsLogsByFilter = (filter, projection, options) => {
        return RewardProgramPointsLogSchema.find(
            filter,
            projection,
            options ?? {
                lean: true
            }
        )
    }

    findOneLogsByOldPointsData = (filter, projection, options) => {
        return RewardProgramPointsLogSchema.findOne(
            filter,
            projection,
            options ?? {
                lean: true
            }
        )
    }

    findPointsLogsWithPagination = async (filter) => {
        const {
            rewardProgramId,
            tenantId,
            rewardProgramMemberId,
            perPage,
            page,
            usedForMobile
        } = filter

        const match = {
            tenant_id: tenantId,
            reward_program_id: rewardProgramId,
            reward_program_member_id: rewardProgramMemberId,
            point_type: REWARD_PROGRAM.POINT.TYPE.COINS
        }

        if (usedForMobile) {
            match.log_type = {
                $nin: [
                    REWARD_PROGRAM.POINT.LOG_TYPE.SAP.INVOICE.CANCELLATION
                ]
            }
        }

        const [list, count] = await Promise.all([
            RewardProgramPointsLogSchema.find(
                match,
                `
                    -created_by
                    -updated_by
                    -__v
                `,
                {
                    limit: perPage,
                    skip: perPage * (page - 1),
                    sort: { log_date: -1 },
                    lean: true
                }
            ),
            RewardProgramPointsLogSchema.countDocuments(match)
        ])

        return { list, count }
    }

    calculatePointsFromStatementSummery = (
        points,
        errors,
        statementSummery,
        statementsDate,
        aging,
        membership,
        rewardProgram,

        // Below params are for debugging purpose
        memberId,
        fromDate,
        customerExternalId,
    ) => {
        const {
            base_amount,
            vip_points_rules
        } = rewardProgram

        const coinRule = rewardProgram[REWARD_PROGRAM.MEMBER.COIN_RULES_CONFIGURATION_KEY[membership]]
        if (!coinRule) {
            logRewardProgram(`For this rewardProgramId (${rewardProgram._id}), memberId (${memberId}) & statementsDate (${statementsDate}), no coin rule found`, {
                tenantId: rewardProgram.tenant_id,
                date: fromDate,
                customerExternalId,
                statementsDate,
                membership,
                rewardProgram,
            })
            return
        }

        // keep order in-sync with destructuring in helper methods
        const debugParams = [
            rewardProgram._id,
            rewardProgram.tenant_id,
            memberId,
            fromDate,
            customerExternalId
        ]

        this.#calculatePointsFromInvoiceSummery(
            points,
            errors,
            statementSummery[SAP_SERVICE.DOCUMENT_TYPE.INVOICE],
            statementsDate,
            coinRule.invoice,
            base_amount,
            ...debugParams,
        )

        this.#calculatePointsFromPaymentSummery(
            points,
            errors,
            statementSummery[SAP_SERVICE.DOCUMENT_TYPE.PAYMENT],
            statementsDate,
            aging,
            coinRule.payment,
            vip_points_rules.payment,
            base_amount,
            ...debugParams,
        )

        this.#calculatePointsFromCreditNotesSummery(
            points,
            errors,
            statementSummery[SAP_SERVICE.DOCUMENT_TYPE.CREDIT_NOTES],
            statementsDate,
            coinRule.credit_notes,
            base_amount,
            ...debugParams,
        )
    }

    #calculatePointsFromInvoiceSummery = (
        points,
        errors,
        invoiceSummery,
        statementsDate,
        invoiceCoinsRules,
        baseAmount,
        ...debugParams
    ) => {
        const [
            rewardProgramId,
            tenantId,
            memberId,
            fromDate,
            customerExternalId,
        ] = debugParams

        logRewardProgram(`For this rewardProgramId (${rewardProgramId}), memberId (${memberId}) & statementsDate (${statementsDate}), invoice summary info`, {
            tenantId,
            date: fromDate,
            customerExternalId,
            invoiceSummery,
            invoiceCoinsRules,
            baseAmount,
        })

        if (!invoiceSummery) {
            return
        }

        if (invoiceSummery.debit_amount > 0) {
            points.push({
                points: Math.ceil(invoiceSummery.debit_amount * (invoiceCoinsRules.purchase / baseAmount)),
                log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SAP.INVOICE.PURCHASE,
                point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                log_date: statementsDate,
                amount: invoiceSummery.debit_amount,
                document_number: invoiceSummery.document_numbers|| 'INVOICE_PURCHASE'
            })
        }

        if (invoiceSummery.credit_amount > 0) {
            points.push({
                points: Math.ceil(invoiceSummery.credit_amount * (invoiceCoinsRules.cancellation / baseAmount)),
                log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SAP.INVOICE.CANCELLATION,
                point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                log_date: statementsDate,
                amount: invoiceSummery.credit_amount,
                document_number: invoiceSummery.document_numbers || 'INVOICE_CANCELLATION'
            })
        }
    }

    #calculatePointsFromPaymentSummery = (
        points,
        errors,
        paymentSummery,
        statementsDate,
        aging,
        paymentCoinsRules,
        paymentVipPointsRules,
        baseAmount,
        ...debugParams
    ) => {
        const [
            rewardProgramId,
            tenantId,
            memberId,
            fromDate,
            customerExternalId,
        ] = debugParams

        logRewardProgram(`For this rewardProgramId (${rewardProgramId}), memberId (${memberId}) & statementsDate (${statementsDate}), payment summary info`, {
            tenantId,
            date: fromDate,
            customerExternalId,
            paymentSummery,
            aging,
            paymentCoinsRules,
            paymentVipPointsRules,
            baseAmount
        })

        if (!paymentSummery || !aging)
            return

        if (paymentSummery.debit_amount > 0) {
            errors.push("Debit amount detected in Payments SAP statements of date: ", statementsDate)
        }

        if (paymentSummery.credit_amount > 0) {
            let remainingAmount = paymentSummery.credit_amount

            const calculations = [
                {
                    aging: Math.round(aging.balance_120_days_above),
                    calculation_type: REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["120_DAYS_ABOVE"],
                    coinsPerSAR: paymentCoinsRules["120_days_above"] / baseAmount,
                    vipPointsPerSAR: paymentVipPointsRules["120_days_above"] / baseAmount,
                },
                {
                    aging: Math.round(aging.balance_91_120_days),
                    calculation_type: REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["91_120_DAYS"],
                    coinsPerSAR: paymentCoinsRules["91_120_days"] / baseAmount,
                    vipPointsPerSAR: paymentVipPointsRules["91_120_days"] / baseAmount,
                },
                {
                    aging: Math.round(aging.balance_61_90_days),
                    calculation_type: REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["61_90_DAYS"],
                    coinsPerSAR: paymentCoinsRules["61_90_days"] / baseAmount,
                    vipPointsPerSAR: paymentVipPointsRules["61_90_days"] / baseAmount,
                },
                {
                    aging: Math.round(aging.balance_31_60_days),
                    calculation_type: REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["31_60_DAYS"],
                    coinsPerSAR: paymentCoinsRules["31_60_days"] / baseAmount,
                    vipPointsPerSAR: paymentVipPointsRules["31_60_days"] / baseAmount,
                },
                {
                    aging: Math.round(aging.balance_0_30_days),
                    calculation_type: REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["0_30_DAYS"],
                    coinsPerSAR: paymentCoinsRules["0_30_days"] / baseAmount,
                    vipPointsPerSAR: paymentVipPointsRules["0_30_days"] / baseAmount,
                }
            ]

            const coinsLog = {
                points: 0,
                log_date: statementsDate,
                log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SAP.PAYMENT,
                point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                amount: paymentSummery.credit_amount,
                document_number: paymentSummery.document_numbers || 'PAYMENT',
                calculations: []
            }

            const vipPointsLog = {
                points: 0,
                log_date: statementsDate,
                log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SAP.PAYMENT,
                point_type: REWARD_PROGRAM.POINT.TYPE.VIP_POINTS,
                amount: paymentSummery.credit_amount,
                document_number: paymentSummery.document_numbers || 'PAYMENT',
                calculations: []
            }

            for (let i = 0; i < calculations.length; i++) {
                const calculation = calculations[i];
                logRewardProgram(`For this rewardProgramId (${rewardProgramId}), memberId (${memberId}) & statementsDate (${statementsDate}), payment summary's calculation info`, {
                    tenantId,
                    date: fromDate,
                    customerExternalId,
                    calculation,
                    remainingAmount,
                })

                if (calculation.aging < 0) {
                    calculation.aging = 0
                }

                let amountUsed = calculation.aging >= remainingAmount ? remainingAmount : calculation.aging
                logRewardProgram(`For this rewardProgramId (${rewardProgramId}), memberId (${memberId}) & statementsDate (${statementsDate}), payment summary's amountUsed is ${amountUsed}`, {
                    tenantId,
                    date: fromDate,
                    customerExternalId,
                })

                if (amountUsed < 0) {
                    amountUsed = 0
                }

                const earnCoins = Math.ceil(amountUsed * calculation.coinsPerSAR)
                logRewardProgram(`For this rewardProgramId (${rewardProgramId}), memberId (${memberId}) & statementsDate (${statementsDate}), payment summary's earnCoins is ${earnCoins}`, {
                    tenantId,
                    date: fromDate,
                    customerExternalId,
                })

                if (earnCoins > 0) {
                    coinsLog.points += earnCoins
                }
                coinsLog.calculations.push({
                    type: calculation.calculation_type,
                    aging: calculation.aging,
                    points: earnCoins,
                    amountUsed
                })

                const earnVipPoints = Math.ceil(amountUsed * calculation.vipPointsPerSAR)
                logRewardProgram(`For this rewardProgramId (${rewardProgramId}), memberId (${memberId}) & statementsDate (${statementsDate}), payment summary's earnVipPoints is ${earnVipPoints}`, {
                    tenantId,
                    date: fromDate,
                    customerExternalId,
                })

                if (earnVipPoints > 0) {
                    vipPointsLog.points += earnVipPoints
                }
                vipPointsLog.calculations.push({
                    type: calculation.calculation_type,
                    aging: calculation.aging,
                    points: earnVipPoints,
                    amountUsed
                })

                remainingAmount = remainingAmount - calculation.aging
            }

            logRewardProgram(`For this rewardProgramId (${rewardProgramId}), memberId (${memberId}) & statementsDate (${statementsDate}), payment summary's coins & vipPoints info`, {
                tenantId,
                date: fromDate,
                customerExternalId,
                coinsPoints: coinsLog.points,
                vipPoints: vipPointsLog.points,
            })

            if (coinsLog.points > 0) {
                points.push(coinsLog)
            }

            if (vipPointsLog.points > 0) {
                points.push(vipPointsLog)
            }
        }
    }

    #calculatePointsFromCreditNotesSummery = (
        points,
        errors,
        creditNotesSummery,
        statementsDate,
        creditNotesCoinsRules,
        baseAmount,
        ...debugParams
    ) => {
        const [
            rewardProgramId,
            tenantId,
            memberId,
            fromDate,
            customerExternalId,
        ] = debugParams

        logRewardProgram(`For this rewardProgramId (${rewardProgramId}), memberId (${memberId}) & statementsDate (${statementsDate}), credit notes summary info`, {
            tenantId,
            date: fromDate,
            customerExternalId,
            creditNotesSummery,
            creditNotesCoinsRules,
            baseAmount,
        })

        if (!creditNotesSummery)
            return

        const returnsSummery = creditNotesSummery[SAP_SERVICE.CREDIT_MEMO_TYPE.RETURNS]
        const discountsSummery = creditNotesSummery[SAP_SERVICE.CREDIT_MEMO_TYPE.DISCOUNT]

        if (returnsSummery) {
            if (returnsSummery.debit_amount > 0) {
                errors.push("Debit amount detected in Returns SAP statements of date: ", statementsDate)
            }

            if (returnsSummery.credit_amount > 0) {
                points.push({
                    points: Math.ceil(returnsSummery.credit_amount * (creditNotesCoinsRules.returns / baseAmount)),
                    log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SAP.CREDIT_NOTES.RETURNS,
                    point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                    log_date: statementsDate,
                    amount: returnsSummery.credit_amount,
                    document_number: returnsSummery.document_numbers|| 'CREDIT_NOTES_RETURNS'
                })
            }
        }

        if (discountsSummery) {
            if (discountsSummery.debit_amount > 0) {
                errors.push("Debit amount detected in Discounts SAP statements of date: ", statementsDate)
            }

            if (discountsSummery.credit_amount > 0) {
                points.push({
                    points: Math.ceil(discountsSummery.credit_amount * (creditNotesCoinsRules.discounts / baseAmount)),
                    log_type: REWARD_PROGRAM.POINT.LOG_TYPE.SAP.CREDIT_NOTES.DISCOUNT,
                    point_type: REWARD_PROGRAM.POINT.TYPE.COINS,
                    log_date: statementsDate,
                    amount: discountsSummery.credit_amount,
                    document_number: discountsSummery.document_numbers || 'CREDIT_NOTES_DISCOUNT'
                })
            }
        }
    }

}
