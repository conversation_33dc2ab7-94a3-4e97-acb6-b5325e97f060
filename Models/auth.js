const ejs = require("ejs");
const path = require("path");

const jwt = require('jsonwebtoken');

const AuthService = new (require("../Services/AuthService"))()

const { generateOtp, stringifyObjectId } = require('../Utils/helpers');
const { sendEmail } = require('../Configs/mailer');

const sendSMS = new (require('../Configs/smsHandler'))();

// const WelcomeTemplate = require("../Views/welcomePortalUserTemp.ejs");
// const OTPTemplate = require("../Views/verifyOTPTemp")
// const AmazonCognitoIdentity = require('amazon-cognito-identity-js');

const RoleModal = new (require("./roles"));

const {
    users: UserSchema,
    tenants: TenantSchema,
    user_roles: UserRoleSchema,
    user_sessions: UserSessionSchema,
    user_devices: UserDeviceSchema,
    roles: RoleSchema,
    cognito_configs: CognitoConfigSchema,
    tenant_customers: TenantCustomerSchema,
    app_login_otps: AppLoginOtpSchema
} = require('../Database/Schemas');

const {
    VALUES,
    SIGNING_TYPE,
    ENVIRONMENTS,
    BY_PASS_OTP,
    PRIMITIVE_ROLES
} = require("../Configs/constants");

class AuthModal {

    async findUserByMobileNumber(mobileNumber, countryCode) {
        return await UserSchema.findOne({ mobile_number: mobileNumber, country_code: countryCode })
    }

    async findUserByEmail(email) {
        return UserSchema.findOne({ email: { $regex: email, $options: "i" } });
    }

    async updateUserRoleByFilter(filter, updateObject, options) {
        return UserRoleSchema.updateOne(filter, updateObject, options);
    }

    async findUserById(_id) {
        return UserSchema.findById(_id);
    }

    async signup(body) {
        return UserSchema({
            first_name: body.firstName,
            last_name: body.lastName,
            email: body.email,
            mobile_number: body.mobileNumber,
            country_code: body.countryCode,
            is_active: body.isActive === undefined ? true : body.isActive,
            is_deleted: false
        });
    }

    async creatUserInUserPool(body) {
        return new Promise((resolve, reject) => {
            const attributeList = [];
            attributeList.push(new AmazonCognitoIdentity.CognitoUserAttribute({ Name: "name", Value: body.firstName + " " + body.lastName }));
            attributeList.push(new AmazonCognitoIdentity.CognitoUserAttribute({ Name: "email", Value: body.email }));
            attributeList.push(new AmazonCognitoIdentity.CognitoUserAttribute({ Name: "phone_number", Value: body.countryCode + body.mobileNumber }));
            const userPool = new AmazonCognitoIdentity.CognitoUserPool(VALUES.userPoolData);
            userPool.signUp(body.email, body.password, attributeList, null, function (err, result) {
                if (err) {
                    reject(err)
                    logger.error(err)
                    return;
                }
                resolve(result);
            });
        });
    }

    async adminConfirmSignUp(body) {
        return new Promise((resolve, reject) => {
            const parmas = {
                UserPoolId: process.env.AWS_USER_POOL_ID,
                Username: body.email,
            }
            AWSAdminCognito.adminConfirmSignUp(parmas, function (err, result) {
                if (err) {
                    resolve({ status: 422, message: "could not confirm the user." })
                    return
                }
                resolve(result);
            })
        })
    }

    async generateTokenFromRefreshToken(refreshToken) {
        return new Promise((resolve, reject) => {
            const parameters = {
                UserPoolId: process.env.AWS_USER_POOL_ID,
                AuthFlow: "REFRESH_TOKEN",
                ClientId: process.env.AWS_USER_POOL_CLIENT_ID,
                AuthParameters: {
                    REFRESH_TOKEN: refreshToken
                }
            };

            AWSAdminCognito.adminInitiateAuth(parameters, function (err, result) {
                if (err) {
                    reject(err)
                    return
                }
                resolve(result);
            });
        });
    }

    async generateAppAccessTokenFromRefreshToken(refreshToken, userRole, loginDate, deviceaccesstype) {
        const { mobile_number, country_code, tenant_customer, user } = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET);
        if (
            stringifyObjectId(userRole.user_id._id) !== user?._id &&
            stringifyObjectId(userRole.user_id._id) !== tenant_customer?._id
        ) {
            const error = new Error("invalid user_id");
            error.name = "invalid_user_id";
            throw error;
        }

        const accessToken = jwt.sign({
            mobile_number, country_code,
            tenant_customer,
            user,
            userRoleId: userRole._id,
            login_time: loginDate.toISOString(),
            deviceaccesstype
        }, process.env.JWT_TOKEN_SECRET, { expiresIn: process.env.JWT_EXPIRE_IN_HOURS });

        return accessToken;

    }

    async verifyUserDetails(body, headers) {
        return new Promise((resolve, reject) => {
            const parameters = {
                Username: body.email,
                UserPoolId: process.env.AWS_USER_POOL_ID,
                UserAttributes: [
                    new AmazonCognitoIdentity.CognitoUserAttribute({ "Name": "phone_number_verified", "Value": "true" }),
                    new AmazonCognitoIdentity.CognitoUserAttribute({ "Name": "email_verified", "Value": "true" }),
                ]
            }
            AWSAdminCognito.adminUpdateUserAttributes(parameters, function (err, result) {
                if (err) {
                    reject(err)
                    return;
                }
                resolve(result);
            })
        })
    }

    /**
     *
     * @param {*} body
     * @param {*} headers
     * @returns {Promise}
     * if countryCode and mobileNumbers are provided, they also will be updated
     */
    async updateCognitoUserNameAndEmail(body, headers) {
        return new Promise((resolve, reject) => {
            const parameters = {
                Username: body.previousEmail, // or cognito username
                UserPoolId: process.env.AWS_USER_POOL_ID,
                UserAttributes: [
                    new AmazonCognitoIdentity.CognitoUserAttribute({ Name: "name", Value: body.firstName + " " + body.lastName }),
                    new AmazonCognitoIdentity.CognitoUserAttribute({ Name: "email", Value: body.email }),
                ]
            }
            if (body.countryCode && body.mobileNumber) {
                parameters.UserAttributes.push(new AmazonCognitoIdentity.CognitoUserAttribute({ Name: "phone_number", Value: body.countryCode + body.mobileNumber }))
            }
            // AWSCognito.adminSetUserPassword
            AWSAdminCognito.adminUpdateUserAttributes(parameters, function (err, result) {
                if (err) {
                    reject(err)
                    return;
                }
                resolve(result);
            })
        })
    }

    async signIn(body, headers) {
        return new Promise((resolve, reject) => {
            const authenticationDetails = new AmazonCognitoIdentity.AuthenticationDetails({
                Username: body.userName,
                Password: body.password,
            });
            this.getCognitoUser(body.userName).authenticateUser(authenticationDetails, {
                onSuccess: function (result) {
                    resolve({ accessToken: result.getAccessToken().getJwtToken(), idToken: result.getIdToken().getJwtToken(), refreshToken: result.getRefreshToken().getToken(), tokenInfo: result.getAccessToken().payload })
                },
                onFailure: function (err) {
                    logger.error(err)
                    reject(err)
                },
            })
        })
    }

    async getUserDetailsFromCognitoUserName(cognito_username, projection = {}, options = {}) {
        return UserSchema.findOne({ cognito_username }, projection, options);
    }

    async getCustomerRolesByUserId(body, headers) {
        const customerRole = await RoleModal.getCustomerRole({ _id: 1 });
        const pipeline = [
            {
                $match: {
                    is_deleted: false,
                    tenant_customer_id: body.user._id,
                    is_active: true,
                    role_id: customerRole._id,
                    customer_app_access: true
                }
            },
            {
                $lookup: {
                    from: "tenants",
                    localField: "tenant_id",
                    foreignField: "_id",
                    as: "tenant_id"
                },
            },
            {
                $lookup: {
                    from: "roles",
                    localField: "role_id",
                    foreignField: "_id",
                    as: "role_id"
                },
            },
            {
                $addFields: {
                    tenant_id: { $first: "$tenant_id" },
                    role_id: { $first: "$role_id" },
                }
            },
            {
                $lookup: {
                    from: "countries",
                    localField: "tenant_id.country",
                    foreignField: "_id",
                    as: "country",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                timezone: 1,
                                country_code: 1,
                                secondary_language_name: 1,
                                secondary_language_code: 1,
                            }
                        }
                    ]
                },
            },
            {
                $lookup: {
                    from: "tenant_app_settings",
                    localField: "tenant_id._id",
                    foreignField: "tenant_id",
                    as: "tenant_app_settings",
                    pipeline: [
                        {
                            $project: {
                                preferred_language: 1,
                            }
                        }
                    ]
                },
            },
            {
                $addFields: {
                    country: { $first: "$country" },
                    tenant_app_settings: { $first: "$tenant_app_settings" },
                }
            },
            {
                $project: {
                    role_id: {
                        _id: "$role_id._id",
                        name: "$role_id.name",
                        portal_type: "$role_id.portal_type",
                        description: "$role_id.description",
                        permission: "$role_id.permission",
                    },
                    tenant_id: {
                        _id: "$tenant_id._id",
                        name: "$tenant_id.name",
                        services: "$tenant_id.services",
                        is_active: "$tenant.is_active",
                        subscription_start_date: "$tenant_id.subscription_start_date",
                        subscription_end_date: "$tenant_id.subscription_end_date",
                        country: "$country",
                        preferred_language: "$tenant_app_settings.preferred_language",
                    },
                    user_id: 1,
                    supervisor_id: 1,
                    is_active: 1,
                    sales_person_id: 1,
                    device_access: 1,
                }
            }
        ];

        return UserRoleSchema.aggregate(pipeline)
    }

    async getTenantInfoWithUserDetails(user_id) {
        const pipeline = [
            {
                $match: {
                    is_deleted: false,
                    user_id,
                    is_active: true
                }
            },
            {
                $lookup: {
                    from: "roles",
                    localField: "role_id",
                    foreignField: "_id",
                    as: "role_id"
                },
            },
            {
                $lookup: {
                    from: "tenant_branches",
                    localField: "branch_id",
                    foreignField: "_id",
                    as: "branch_id"
                },
            },
            {
                $lookup: {
                    from: "tenants",
                    localField: "tenant_id",
                    foreignField: "_id",
                    as: "tenant_id"
                },
            },
            {
                $addFields: {
                    tenant_id: { $first: "$tenant_id" },
                    branch_id: { $first: "$branch_id" },
                    role_id: { $first: "$role_id" },
                }
            },
            {
                // fetching linked tenant information for Tenant Owner and Tenant Admin profiles
                $lookup: {
                    from: "tenants",
                    localField: "tenant_id.linked_tenant_id",
                    foreignField: "_id",
                    as: "tenant_id.linked_tenant_id",
                    let: { portal_type: "$role_id.portal_type" },
                    pipeline: [
                        {
                            $match: {
                                is_active: true,
                                is_deleted: false,
                                $expr: {
                                    $eq: ["$$portal_type", VALUES.portals.TENANT_PORTAL]
                                }
                            }
                        },
                        {
                            $lookup: {
                                from: "tenant_app_settings",
                                localField: "_id",
                                foreignField: "tenant_id",
                                as: "tenant_app_settings",
                                pipeline: [
                                    {
                                        $project: {
                                            preferred_language: 1,
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: "countries",
                                localField: "country_id",
                                foreignField: "id",
                                as: "country",
                                pipeline: [
                                    {
                                        $project: {
                                            name: 1,
                                            timezone: 1,
                                            country_code: 1,
                                            secondary_language_name: 1,
                                            secondary_language_code: 1,
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $addFields: {
                                tenant_app_settings: { $first: "$tenant_app_settings" },
                                country: { $first: "$country" }
                            }
                        },
                        {
                            $project: {
                                name: 1,
                                subscription_start_date: 1,
                                subscription_end_date: 1,
                                country: 1,
                                preferred_language: "$tenant_app_settings.preferred_language"
                            }
                        },

                    ]
                }
            },
            {
                $lookup: {
                    from: "countries",
                    localField: "tenant_id.country",
                    foreignField: "_id",
                    as: "country",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                timezone: 1,
                                country_code: 1,
                                secondary_language_name: 1,
                                secondary_language_code: 1,
                            }
                        }
                    ]
                },
            },
            {
                $lookup: {
                    from: "tenant_app_settings",
                    localField: "tenant_id._id",
                    foreignField: "tenant_id",
                    as: "tenant_app_settings",
                    pipeline: [
                        {
                            $project: {
                                preferred_language: 1,
                            }
                        }
                    ]
                },
            },
            {
                $addFields: {
                    country: { $first: "$country" },
                    tenant_app_settings: { $first: "$tenant_app_settings" },
                }
            },
            {
                $project: {
                    role_id: {
                        _id: "$role_id._id",
                        name: "$role_id.name",
                        portal_type: "$role_id.portal_type",
                        description: "$role_id.description",
                        permission: "$role_id.permission",
                    },
                    tenant_id: {
                        _id: "$tenant_id._id",
                        name: "$tenant_id.name",
                        services: "$tenant_id.services",
                        is_active: "$tenant.is_active",
                        subscription_start_date: "$tenant_id.subscription_start_date",
                        subscription_end_date: "$tenant_id.subscription_end_date",
                        country: "$country",
                        preferred_language: "$tenant_app_settings.preferred_language",
                        linked_tenants: "$tenant_id.linked_tenant_id"
                    },
                    branch_id: {
                        _id: "$branch_id._id",
                        name: "$branch_id.name",
                        is_active: "$branch_id.is_active",
                        tenant_id: "$branch_id.tenant_id",
                    },
                    user_id: 1,
                    supervisor_id: 1,
                    is_active: 1,
                    profile_pic: 1
                }
            }
        ];
        return UserRoleSchema.aggregate(pipeline)
    }

    async getNonDeletedPortalUserRolesById(user_id, deviceType) {
        if (!(deviceType === VALUES.DEVICE_TYPE.DESKTOP || deviceType === VALUES.DEVICE_TYPE.WEB)) {
            return this.getUserNonDeletedRolesById(user_id);
        }

        const pipeline = [
            {
                $match: {
                    is_deleted: false,
                    user_id,
                    is_active: true
                }
            },
            {
                $lookup: {
                    from: "roles",
                    localField: "role_id",
                    foreignField: "_id",
                    as: "role_id"
                },
            },
            {
                $lookup: {
                    from: "tenant_branches",
                    localField: "branch_id",
                    foreignField: "_id",
                    as: "branch_id"
                },
            },
            {
                $lookup: {
                    from: "tenants",
                    localField: "tenant_id",
                    foreignField: "_id",
                    as: "tenant_id"
                },
            },
            {
                $addFields: {
                    tenant_id: { $first: "$tenant_id" },
                    branch_id: { $first: "$branch_id" },
                    role_id: { $first: "$role_id" },
                }
            },
            {
                $match: {
                    "role_id.portal_type": { $in: [VALUES.portals.SYSTEM_PORTAL, VALUES.portals.TENANT_PORTAL, VALUES.portals.BRANCH_PORTAL] }
                }
            },
            {
                // fetching linked tenant information for Tenant Owner and Tenant Admin profiles
                $lookup: {
                    from: "tenants",
                    localField: "tenant_id.linked_tenant_id",
                    foreignField: "_id",
                    as: "tenant_id.linked_tenant_id",
                    let: { portal_type: "$role_id.portal_type" },
                    pipeline: [
                        {
                            $match: {
                                is_active: true,
                                is_deleted: false,
                                $expr: {
                                    $eq: ["$$portal_type", VALUES.portals.TENANT_PORTAL]
                                }
                            }
                        },
                        {
                            $lookup: {
                                from: "tenant_app_settings",
                                localField: "_id",
                                foreignField: "tenant_id",
                                as: "tenant_app_settings",
                                pipeline: [
                                    {
                                        $project: {
                                            preferred_language: 1,
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: "countries",
                                localField: "country_id",
                                foreignField: "id",
                                as: "country",
                                pipeline: [
                                    {
                                        $project: {
                                            name: 1,
                                            timezone: 1,
                                            country_code: 1,
                                            secondary_language_name: 1,
                                            secondary_language_code: 1,
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $addFields: {
                                tenant_app_settings: { $first: "$tenant_app_settings" },
                                country: { $first: "$country" }
                            }
                        },
                        {
                            $project: {
                                name: 1,
                                subscription_start_date: 1,
                                subscription_end_date: 1,
                                country: 1,
                                preferred_language: "$tenant_app_settings.preferred_language"
                            }
                        },

                    ]
                }
            },
            {
                $lookup: {
                    from: "countries",
                    localField: "tenant_id.country",
                    foreignField: "_id",
                    as: "country",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                timezone: 1,
                                country_code: 1,
                                secondary_language_name: 1,
                                secondary_language_code: 1,
                            }
                        }
                    ]
                },
            },
            {
                $lookup: {
                    from: "tenant_app_settings",
                    localField: "tenant_id._id",
                    foreignField: "tenant_id",
                    as: "tenant_app_settings",
                    pipeline: [
                        {
                            $project: {
                                preferred_language: 1,
                            }
                        }
                    ]
                },
            },
            {
                $addFields: {
                    country: { $first: "$country" },
                    tenant_app_settings: { $first: "$tenant_app_settings" },
                }
            },
            {
                $project: {
                    role_id: {
                        _id: "$role_id._id",
                        name: "$role_id.name",
                        portal_type: "$role_id.portal_type",
                        description: "$role_id.description",
                        permission: "$role_id.permission",
                    },
                    tenant_id: {
                        _id: "$tenant_id._id",
                        name: "$tenant_id.name",
                        services: "$tenant_id.services",
                        is_active: "$tenant.is_active",
                        subscription_start_date: "$tenant_id.subscription_start_date",
                        subscription_end_date: "$tenant_id.subscription_end_date",
                        country: "$country",
                        preferred_language: "$tenant_app_settings.preferred_language",
                        linked_tenants: "$tenant_id.linked_tenant_id"
                    },
                    branch_id: {
                        _id: "$branch_id._id",
                        name: "$branch_id.name",
                        is_active: "$branch_id.is_active",
                        tenant_id: "$branch_id.tenant_id",
                    },
                    user_id: 1,
                    supervisor_id: 1,
                    is_active: 1,
                    profile_pic: 1
                }
            }
        ];

        return UserRoleSchema.aggregate(pipeline)
    }
    /**
     * This method will return a POJO ( leaned object from DB )
     * @param {ObjectId} user_id
     * @returns
     */
    async getUserNonDeletedRolesById(user_id) {
        return await UserRoleSchema.find({ user_id, is_deleted: false })
            .populate({ path: 'role_id', select: 'name portal_type description _id permission' })
            .populate({ path: 'tenant_id', select: '_id name services is_active subscription_start_date subscription_end_date' })
            .populate({ path: 'branch_id', select: '_id name' })
            .lean()
            .exec();

        // spread each branch access to sing object for Branch Manager role in BRANCH_PORTAL
        // const result = [];
        // userRoles?.forEach(ur => {

        //     if(ur.role_id.portal_type === VALUES.portals.BRANCH_PORTAL && ur.role_id.name.toLowerCase() === "branch manager") {
        //         if(Array.isArray(ur.branch_id)) {
        //             ur.branch_id.forEach(br => {
        //                 result.push(
        //                     {
        //                         ...ur,
        //                         branch_id: { ...br }
        //                     }
        //                 )
        //             })
        //         }
        //     }
        //     result.push({ ...ur,  });
        // });
        // return result;
    }

    async findUserDevice(device_token, user_id) {
        return UserDeviceSchema.findOne({ device_token, user_id });
    }

    async getUserDeviceInfo(user_id, device_token) {
        return await UserDeviceSchema.findOne({ user_id, device_token });
    }

    async modifyDeviceTokenInfo(device_token, userDetails, signInType, isOtpSentOnWhatsapp, tenantId, res) {
        let otp;
        if (userDetails.email === "<EMAIL>" || ((userDetails.country_code + userDetails.mobile_number === "+919998977764"))) {
            otp = String(BY_PASS_OTP);
        } else {
            otp = generateOtp();
        }
        if (signInType === SIGNING_TYPE.EMAIL) {
            // await this.sendEmailOTP(userDetails.email, otp, "Verification OTP");
            //TODO: need to add language support and replace the undefined
            await this.sendOtpVerificationEMail({ username: userDetails.first_name + " " + userDetails.last_name, otp_num: otp, rtl: false }, undefined, userDetails.email)
        } else if (signInType === SIGNING_TYPE.MOBILE) {
            if (isOtpSentOnWhatsapp) {
                const configurations = await AuthService.findIntegrationCredentialModel(tenantId);
                if (!configurations) {
                    return res.handler.notFound('message_bird_credentials_not_found');
                }
                if (!configurations.is_active) {
                    return res.handler.conflict('message_bird_credentials_inactive');
                }
                const [
                    whatsAppResult,
                    mailResult
                ] = await Promise.allSettled([
                    AuthService.sendWhatsAppMessage(configurations, `${userDetails.country_code}${userDetails.mobile_number}`, otp),
                    this.sendOtpVerificationEMail({ username: userDetails.first_name + " " + userDetails.last_name, otp_num: otp, rtl: false }, undefined, userDetails.email)
                ])

                if (whatsAppResult.reason) {
                    logger.error(whatsAppResult.reason)
                }

                if (mailResult.reason) {
                    logger.error(mailResult.reason)
                }

                if (whatsAppResult.status === "rejected" && mailResult.status === "rejected") {
                    throw [
                        whatsAppResult.reason,
                        mailResult.reason
                    ]
                }
            }
            else {
                const [
                    smsResult,
                    mailResult
                ] = await Promise.allSettled([
                    this.sendMobileOTP((userDetails.country_code + userDetails.mobile_number).replace("+", ""), otp),
                    this.sendOtpVerificationEMail({ username: userDetails.first_name + " " + userDetails.last_name, otp_num: otp, rtl: false }, undefined, userDetails.email)
                ])

                if (smsResult.reason) {
                    logger.error(smsResult.reason)
                }

                if (mailResult.reason) {
                    logger.error(mailResult.reason)
                }

                if (smsResult.status === "rejected" && mailResult.status === "rejected") {
                    throw [
                        smsResult.reason,
                        mailResult.reason
                    ]
                }
            }
        }
        const filter = {
            user_id: userDetails._id,
            device_token,

        };
        const update = {
            user_id: userDetails._id,
            device_token,
            otp,
            device_verified: false
        };

        await UserDeviceSchema.findOneAndUpdate(filter, update, { runValidators: true, upsert: true, })
    }

    async startUserSession(device_token, device_type, tokenInfo, userDetails, collection_name = "users") {
        const userSession = await UserSessionSchema({
            access_token: tokenInfo.accessToken,
            status: VALUES.sessionStatus.ACTIVE,
            total_session_time_in_sec: Math.round(tokenInfo.tokenInfo.exp / 1000),
            user_id: userDetails._id,
            device_token,
            device_type,
            start_time: new Date(),
            collection_name,
        })

        await userSession.save();
    }

    async addAppLoginOtp(object = {}) {
        return new AppLoginOtpSchema(object);
    }

    async getAppOtpWithFilter(filter, projection, options) {
        return AppLoginOtpSchema.findOne(filter, projection, options);
    }

    async newUserSession() {
        return new UserSessionSchema();
    }

    async startUserSessionWithUserRoleId(device_token, device_type, tokenInfo, userDetails, userRole, tenant_id, branch_id) {
        const userSession = await UserSessionSchema({
            access_token: tokenInfo.accessToken,
            status: VALUES.sessionStatus.ACTIVE,
            total_session_time_in_sec: Math.round(tokenInfo.tokenInfo.exp / 1000),
            user_id: userDetails._id,
            device_token,
            device_type,
            start_time: new Date(),
            user_role_id: userRole._id,
            tenant_id,
            branch_id,
            collection_name: userRole.collection_name || "tenant_customers"
            // refresh_token: tokenInfo.refreshToken,
        })

        await userSession.save()
    }

    async inActivatePreviousUserSessions(userDetails) {
        return UserSessionSchema.updateMany({ user_id: userDetails._id }, { status: VALUES.sessionStatus.CLOSE, end_time: new Date() });
    }

    async removeOldUserSessionDocs() {
        const response = await UserSessionSchema.deleteMany({ created_at: { $lte: moment().subtract(1, 'months').toDate() } })
        logger.info(`Message: Total deleted user_sessions ${response.deletedCount}\n`)
    }

    async sendMobileOTP(recipientNumber, OTP) {
        const body = `Your OTP for verification is ${OTP}`;
        await sendSMS.sendSMS(recipientNumber, body)
    }

    async sendMobileVerification(recipientNumber, OTP) {
        const body = `Please click below link to generate new password. auth/create-new-password?verifier=${OTP}`;
        await sendSMS.sendSMS(recipientNumber, body)
    }

    async sendEmailOTP(receiverEMail, OTP, subject = "Verification OTP") {
        const body = {
            text: "",
            html: "",
        };
        body.text = `Your verification OTP is ${OTP}`;
        body.html = `<p>Your verification OTP is <strong>${OTP}</strong></p>`
        const response = await sendEmail(receiverEMail, subject, body)
        logger.info("Email sending response:", response)
        return response
    }

    async sendOtpVerificationEMail(items = { username: "", otp_num: 6542, rtl: false }, user_preferred_language = "en", email = "", emailSubject = "Verify OTP") {
        const font = {
            en: {
                "hi": "Hi",
                "otp_paragraph": "Here is your One Time Password:",
                "valid_paragraph": "Valid for 5 minutes.",
                "thanks": "Thanks",
                "team_hawak": "Team Hawak",
                "copy_rights": "Copyright © 2023 Hawak, All Rights Reserved.",
                "address": "Al Rayan District, Riyadh, Saudi Arabia",
                "contact": "Contact us • Privacy Policy • Terms of Use"
            },
            ar: {
                "hi": "أهلاً",
                "otp_paragraph": "ها هي كلمة مرورك لمرة واحدة:",
                "valid_paragraph": "صالحة لمدة 5 دقائق.",
                "thanks": "شكرًا",
                "team_hawak": "فريق القابضة",
                "copy_rights": "حقوق النشر © 2023 Hawak ، جميع الحقوق محفوظة.",
                "address": "حي الريان ، الرياض ، المملكة العربية السعودية",
                "contact": "اتصل بنا • سياسة الخصوصية • شروط الاستخدام"
            }
        }

        const html = await ejs.renderFile(path.join(__dirname, "../Views/verifyOTPTemp.ejs"), { items, fonts: font[user_preferred_language] || font["en"] });

        await sendEmail(email, emailSubject, { html })
    }

    async resetPasswordLinkEmail(items = { username: "", reset_link: "", rtl: false }, email = "", subject = "Verification link", user_preferred_language = "en") {
        const font = {
            en: {
                "hi": "Hi",
                "req_paragraph": "You have requested us to send you a link to reset your password.",
                "reset_btn": "Reset Password",
                "safety_paragraph": "If you didnt initiate the request, you can safely ignore this email.",
                "thanks": "Thanks",
                "team_hawak": "Team Hawak",
                "copy_rights": "Copyright © 2023 Hawak, All Rights Reserved.",
                "address": "Al Rayan District, Riyadh, Saudi Arabia",
                "contact": "Contact us • Privacy Policy • Terms of Use"
            },
            ar: {
                "hi": "أهلاً",
                "req_paragraph": "لقد طلبت منا أن نرسل لك رابطًا لإعادة تعيين كلمة المرور الخاصة بك.",
                "reset_btn": "إعادة تعيين كلمة المرور",
                "safety_paragraph": "إذا لم تبدأ الطلب ، فيمكنك تجاهل هذا البريد الإلكتروني بأمان.",
                "thanks": "شكرًا",
                "team_hawak": "فريق القابضة",
                "copy_rights": "حقوق النشر © 2023 Hawak ، جميع الحقوق محفوظة.",
                "address": "حي الريان ، الرياض ، المملكة العربية السعودية",
                "contact": "اتصل بنا • سياسة الخصوصية • شروط الاستخدام"
            }
        }
        const html = await ejs.renderFile(path.join(__dirname, "../Views/resetPasswordTemp.ejs"), { items, fonts: font[user_preferred_language] || font["en"] });
        await sendEmail(email, subject, { html });
    }

    async orderInfoEmail(
        items = {
            "_id": "",
            "order_id": 1105445342,
            "order_date": "November 17, 2022",
            "customer_legal_name": "تالاتلنلتالنتالن  اتلن الن الن تانتلاتلن اتلنلنالنلنتالنالنلنتال التنالنالنتانتلن",
            "num_of_items": "1,290",
            "total_qty": "10,162",
            "order_amount": "12,300.50 SAR",
            "status": ["Received", "Preparing", "Shipped", "Delivered"],
            "status_code": 2,
            "rtl": false,

            company_region: "",
            company_legal_name: "",
            company_city: "",
            company_phone: "",
            company_address: "",

            sales_person_mobile: "",
            salesperson_name: "",

            shipping_address: "",
            shipping_region: "",
            shipping_city: "",
            shipping_mobile_number: "",

            order_link: ""
        },
        user_preferred_language = "en",
        email = "",
        subject = "Order info received") {

        let fonts = {
            en: {
                "order_p": "",
                "order_ps": ["We have received your order", "Your order is being processed", "Your order has been shipped", "Your order has been delivered"],
                "order_desc_p": "",
                "order_desc_ps": [ // translation needed, therefore, kept it here
                    "Here is a summary of your order. We will be processing it shortly.",
                    "Hang tight! We are processing your order.",
                    "Great News! We have shipped your order.",
                    "Your order has been delivered"
                ],
                "hawak_link": `${VALUES.WEB_APP_URL}orders/${items._id}`,
                "order_info_label": "Order Information",
                "order_from_label": "Ordered from",
                "order_id": "Order ID",
                "order_date": "Order Date",
                "sales_person": "Sales Person",
                "region_label": "Region",
                "city_label": "City",
                "num_of_items": "Number of items:",
                "total_qty": "Total Quantity:",
                "delivery_address_label": "Delivery Address",
                "address_label": "Address",
                "order_summery": "Order Summary",
                "order_paced_with": "Order placed with:",
                "order_amount": "Order Amount:",
                "track_order_label": "Track Order",
                "track_order": "Track Order",
                "view_details": "View Details",
                "mobile_number_label": "Mobile Number",
                "salesperson_label": "Sales Person",
                "company_details": "Company Details",
                "thanks": "Thanks",
                "team_hawak": "Team Hawak",
                "copy_rights": "Copyright © 2023 Hawak, All Rights Reserved.",
                "address": "Al Rayan District, Riyadh, Saudi Arabia",
                "contact": "Contact us • Privacy Policy • Terms of Use",
            },
            ar: {
                "order_p": "",
                "order_ps": ["We have received your order", "Your order is being processed", "Your order has been shipped", "Your order has been delivered"],
                "order_desc_ps": [ // translation needed, therefore, kept it here
                    "Here is a summary of your order. We will be processing it shortly.",
                    "Hang tight! We are processing your order.",
                    "Great News! We have shipped your order.",
                    "Your order has been delivered"
                ],
                "order_desc_p": "",
                "hawak_link": `${VALUES.WEB_APP_URL}orders/${items._id}`,
                "order_from_label": "Ordered from",
                "order_id": "Order ID",
                "order_date": "Order Date",
                "sales_person": "Sales Person",
                "order_summery": "Order Summary",
                "num_of_items": "Number of items:",
                "region_label": "Region",
                "city_label": "City",
                "order_paced_with": "Order placed with:",
                "total_qty": "Total Quantity:",
                "delivery_address": "Delivery Address",
                "address_label": "Address",
                "order_amount": "Order Amount:",
                "track_order_label": "Track Order",
                "mobile_number_label": "Mobile Number",
                "salesperson_label": "Sales Person",
                "company_details": "Company Details",
                "thanks": "شكرًا",
                "team_hawak": "فريق القابضة",
                "copy_rights": "حقوق النشر © 2023 Hawak ، جميع الحقوق محفوظة.",
                "address": "حي الريان ، الرياض ، المملكة العربية السعودية",
                "contact": "اتصل بنا • سياسة الخصوصية • شروط الاستخدام"
            }
        };

        let mediaQuery = [];
        let boxWidth = 140;

        for (var i = 200; i <= 600; i = i + 10) {
            mediaQuery.push(i);
            if (i === 600) {
                mediaQuery.reverse();
            }
        }

        const tranFonts = { ...fonts[user_preferred_language] } || { ...fonts["en"] };

        tranFonts["order_p"] = tranFonts["order_ps"]?.[items.status_code] || tranFonts["order_ps"][0];
        tranFonts["order_desc_p"] = tranFonts["order_desc_ps"]?.[items.status_code] || tranFonts["order_desc_ps"][0];
        tranFonts["app_store_url"] = process.env.APPSTORE_URL;
        tranFonts["play_store_url"] = process.env.PLAYSTORE_URL;
        tranFonts["hawak_link"] = items.order_link;
        tranFonts["track_order_label"] = items.status_code === 3 ? tranFonts.view_details : tranFonts.track_order;
        const html = await ejs.renderFile(path.join(__dirname, "../Views/orderInfoTemp.ejs"), { items: items, fonts: tranFonts, mediaQuery, boxWidth });
        await sendEmail(email, subject, { html }, [], "<EMAIL>");
    }

    async newOrderEmail(
        items = {
            _id: "",
            "order_id": 1105445342,
            "order_date": "November 17, 2022",
            "salesperson_name": "Mohammad Terkawi",
            "customer_id": 1105445342,
            "customer_legal_name": "تالاتلنلتالنتالن  اتلن الن الن تانتلاتلن اتلنلنالنلنتالنالنلنتال التنالنالنتانتلن",
            "num_of_items": "1,290",
            "total_qty": "10,162",
            "order_amount": "12,300.50 SAR",
            "rtl": false,
            order_link: ""
        },
        user_preferred_language = "en",
        email = "",
        subject = "New Order received"
    ) {

        const fonts = {
            en: {
                "order_p": "You've got a new order",
                "order_desc_p": "Here is a summary of your order. For more details visit your dashboard on ",
                "hawak_link": VALUES.WEB_APP_URL,
                hawak_order_link: items.order_link || `${VALUES.WEB_APP_URL}orders/${items._id}`,
                "order_id": "Order ID",
                "order_date": "Order Date",
                "salesperson_name": "Sales Person",
                "customer_id": "Customer ID",
                "customer_legal_name": "Customer Name",
                "num_of_items": "Number of items:",
                "total_qty": "Total Quantity:",
                "order_amount": "Order Amount:",
                "process_order": "Process Order",
                "thanks": "Thanks",
                "team_hawak": "Team Hawak",
                "copy_rights": "Copyright © 2023 Hawak, All Rights Reserved.",
                "address": "Al Rayan District, Riyadh, Saudi Arabia",
                "contact": "Contact us • Privacy Policy • Terms of Use"
            },
            ar: {
                "order_p": "You've got a new order",
                "order_desc_p": "Here is a summary of your order. For more details visit your dashboard on ",
                "hawak_link": VALUES.WEB_APP_URL,
                hawak_order_link: items.order_link || `${VALUES.WEB_APP_URL}orders/${items._id}`,
                "order_id": "Order ID",
                "order_date": "Order Date",
                "salesperson_name": "Sales Person",
                "customer_id": "Customer ID",
                "customer_legal_name": "Customer Name",
                "num_of_items": "Number of items:",
                "total_qty": "Total Quantity:",
                "order_amount": "Order Amount:",
                "process_order": "Process Order",
                "thanks": "شكرًا",
                "team_hawak": "فريق القابضة",
                "copy_rights": "حقوق النشر © 2023 Hawak ، جميع الحقوق محفوظة.",
                "address": "حي الريان ، الرياض ، المملكة العربية السعودية",
                "contact": "اتصل بنا • سياسة الخصوصية • شروط الاستخدام"
            }
        }

        const html = await ejs.renderFile(path.join(__dirname, "../Views/newOrderTemp.ejs"), { items: items, fonts: fonts[user_preferred_language] || fonts["en"] });

        await sendEmail(email, subject, { html }, [], "<EMAIL>");
    }



    //username is customer name for the tenant
    async welcomeCustomer(details = {
        "order_id": 1105445342,
        "order_date": "November 17, 2022",
        "sales_person": "Mohammad Terkawi",
        "cust_id": 1105445342,
        "cust_name": "تالاتلنلتالنتالن  اتلن الن الن تانتلاتلن اتلنلنالنلنتالنالنلنتال التنالنالنتانتلن",
        "num_of_items": "1,290",
        "total_qty": "10,162",
        "order_amount": "12,300.50 SAR",
        "rtl": false
    }, customerDetails = { lat: 23.033863, lng: 72.585022, shipping_address: "", sales_person: "", region: "", city: "", customer_email: "", mobile_number: 0, country_code: "" },
        user_preferred_language = "en"
    ) {

        const fonts = {
            en: {
                "heading_cust": "New Customer",
                "new_cust": "has added a new customer. Here are the details",
                "cust_id": "Customer ID",
                "cust_name": "Customer Name",
                "address_f": "Address",
                "region": "Region",
                "city": "City",
                "mobile_number": "Mobile Number",
                "thanks": "Thanks",
                "team_hawak": "Team Hawak",
                "copy_rights": "Copyright © 2023 Hawak, All Rights Reserved.",
                "address": "Al Rayan District, Riyadh, Saudi Arabia",
                "contact": "Contact us • Privacy Policy • Terms of Use"
            },
            ar: {
                "heading_cust": "New Customer",
                "new_cust": "has added a new customer. Here are the details",
                "cust_id": "Customer ID",
                "cust_name": "Customer Name",
                "address_f": "Address",
                "region": "Region",
                "city": "City",
                "mobile_number": "Mobile Number",
                "thanks": "شكرًا",
                "team_hawak": "فريق القابضة",
                "copy_rights": "حقوق النشر © 2023 Hawak ، جميع الحقوق محفوظة.",
                "address": "حي الريان ، الرياض ، المملكة العربية السعودية",
                "contact": "اتصل بنا • سياسة الخصوصية • شروط الاستخدام"
            }
        };

        var items = {
            "cust_id": 1105445342,
            "cust_name": "تالاتلنلتالنتالن  اتلن الن الن تانتلاتلن اتلنلنالنلنتالنالنلنتال التنالنالنتانتلن",
            "address": "1011 Prince Whatever Street",
            "region": "Riyadh Region",
            "city": "Riyadh",
            "mobile_number": "+966 50 331 2200",
            "rtl": false,
            "base64Image": `${process.env.API_GATEWAY_BASE_URL}/auth/generateMapFile?lat=${customerDetails.lat}&lng=${customerDetails.lng}`,
        };
        if (customerDetails.customer_email) {
            const html = await ejs.renderFile(path.join(__dirname, "../Views/welcomeCustomer.ejs"), { items: items, fonts: fonts[user_preferred_language] || fonts["en"] });
            await sendEmail(customerDetails.customer_email, "Welcome to Hawak.io", { html });
        }
    }

    async sendEMailVerificationLink(receiverEMail, OTP, userId, subject = "verification link") {
        const body = {
            text: "",
            html: "",
        };
        body.text = `Please click below link to generate new password.`;
        body.html = `<p>Please click below link to generate new password.</p> <a href="${VALUES.WEB_APP_URL}auth/create-new-password?verifier=${OTP}&id=${userId}">CLICK HERE</a>`
        await sendEmail(receiverEMail, subject, body)
    }

    async sendWelcomePortalUser(userDetails = {}, user_preferred_language = "en", rtl = false) {
        const fonts = {
            en: {
                "hi": "Hi",
                "welcome_p": "Welcome to Hawak. We are thrilled to have you onboard.",
                "lets_p": "Lets get you started.",
                "upload_p": "1. Upload your products on",
                "upload_p_sec": "2. Download the Hawak app on:",
                "login_btn": "Login to Portal",
                "thanks": "Thanks",
                "team_hawak": "Team Hawak",
                "copy_rights": "Copyright © 2023 Hawak, All Rights Reserved.",
                "address": "Al Rayan District, Riyadh, Saudi Arabia",
                "contact": "Contact us • Privacy Policy • Terms of Use"
            },
            ar: {
                "hi": "Hi",
                "welcome_p": "Welcome to Hawak. We are thrilled to have you onboard.",
                "lets_p": "Lets get you started.",
                "upload_p": "1. Upload your products on",
                "upload_p_sec": "2. Download the Hawak app on:",
                "login_btn": "Login to Portal",
                "thanks": "شكرًا",
                "team_hawak": "فريق القابضة",
                "copy_rights": "حقوق النشر © 2023 Hawak ، جميع الحقوق محفوظة.",
                "address": "حي الريان ، الرياض ، المملكة العربية السعودية",
                "contact": "اتصل بنا • سياسة الخصوصية • شروط الاستخدام"
            }
        };

        const items = {
            portal_link: VALUES.WEB_APP_URL,
            rtl,
            first_name: userDetails.first_name,
            app_store_url: process.env.APPSTORE_URL,
            play_store_url: process.env.PLAYSTORE_URL
        }
        const html = await ejs.renderFile(path.join(__dirname, "../Views/welcomePortalUserTemp.ejs"), { items: items, fonts: fonts[user_preferred_language] || fonts["en"] });
        await sendEmail(userDetails.email, "Welcome to Hawak.io", { html });
    }

    async verifyUserOTP(body, headers) {
        const userSession = await this.findUserDevice(headers.devicetoken, headers.userDetails._id);
        if (!userSession) {
            return false
        }

        if (body.confirmationCode === String(BY_PASS_OTP) && !VALUES.IS_PROD_ENV) {
            userSession.device_verified = true;
            userSession.otp = undefined;
            await userSession.save();
            return true;
        }

        if (userSession.otp !== body.confirmationCode) {
            return false;
        }
        userSession.device_verified = true;
        userSession.otp = undefined;
        await userSession.save();
        return true;
    }

    async verifyForgotPasswordOTP(body, headers) {
        // {$or: [{email: "<EMAIL>"},{$and: [{mobile_number: null}, {country_code: null}] } ]}
        return UserSchema.findOne({ forgot_password_otp: body.token });
    }

    async updateUserPassword(newPassword, userDetails) {
        return new Promise((resolve, reject) => {
            const request = {
                UserPoolId: process.env.AWS_USER_POOL_ID,
                Username: userDetails.email,
                Password: newPassword,
                Permanent: true
            }
            AWSAdminCognito.adminSetUserPassword(request, function (err, result) {
                if (err) {
                    return reject(err);
                }
                resolve(result)
            })

        })
    }

    async getTokenConfigInfo() {
        return CognitoConfigSchema.findOne();

    }

    async updateTokenConfigInfo(body, headers) {
        return new Promise((resolve, reject) => {
            const request = {
                UserPoolId: process.env.AWS_USER_POOL_ID,
                ClientId: process.env.AWS_USER_POOL_CLIENT_ID,
                AccessTokenValidity: body.time,
                TokenValidityUnits: {
                    AccessToken: body.timeUnit
                }
            }
            AWSAdminCognito.updateUserPoolClient(request, function (err, result) {
                if (err) {
                    return reject(err)
                }
                resolve(result);
            })
        })
    }

    async getUserRoleById(role_id, projection = {}, options = {}) {
        return UserRoleSchema.findById(role_id, projection, options).populate({ path: 'role_id' });
    }


    async getUserRoleWithPopulation(filter, projection, options = {}, populationArry = []) {
        let query = UserRoleSchema.findOne(filter, projection, options);

        for (let i = 0; i < populationArry.length; i++) {
            const populateObj = populationArry[i];
            query = query.populate({ ...populateObj });
        }
        if (options?.lean) {
            query = query.lean();
        }
        return query;
    }

    async getUserRolesWithAggregation(pipeline) {
        return UserRoleSchema.aggregate(pipeline);
    }

    async getUserRolesWithPopulation(filter, projection, options = {}, populationArry = []) {
        let query = UserRoleSchema.find(filter, projection, options);

        for (let i = 0; i < populationArry.length; i++) {
            const populateObj = populationArry[i];
            query = query.populate({ ...populateObj });
        }
        if (options?.lean) {
            query = query.lean();
        }
        return query;
    }

    findUserRoles(filter, projection, options) {
        return UserRoleSchema.find(filter, projection, options)
    }

    /**
     *
     * @param {mongoose.Types.ObjectId} user_id
     * @param {mongoose.Types.ObjectId} tenant_customer_id
     * @param {Boolean} getCount
     * @returns
     */
    async mobileAppProfileWithFilter(
        user_id,
        tenant_customer_id,
        getSalesPersonName = false,
        deviceaccesstype = VALUES.deviceAccessType.TABLET,
    ) {

        let roleFilterCondition = {
            portal_type: {
                $in: [
                    VALUES.portals.SALES_APP,
                    VALUES.portals.TENANT_PORTAL,
                    VALUES.portals.SUPERVISOR_APP,
                ]
            },
            name: {
                $nin: [
                    PRIMITIVE_ROLES.ACCOUNTANT,
                    PRIMITIVE_ROLES.WAREHOUSE_CLERK,
                    PRIMITIVE_ROLES.CONTRIBUTOR
                ]
            }
        }

        //TO RESTRICT THE CUSTOMER FOR ACCESSING CUSTOMER APPLICATION FROM TABLET
        if (deviceaccesstype === VALUES.deviceAccessType.MOBILE) {
            roleFilterCondition.portal_type.$in.push(VALUES.portals.CUSTOMER_APP)
        }

        const appProfileRoleIds = await RoleModal.getRolesWithFilter(roleFilterCondition, { _id: 1 });
        const $match = {
            role_id: { $in: appProfileRoleIds.map(ap => ap._id) },
            $or: [],
            // is_active: true, // TODO make this paramter inactive
            is_deleted: false
        };

        if (user_id) {
            $match["$or"].push({ user_id })
        }
        if (tenant_customer_id) {
            // tenant app setting check for customer app access
            // customer app access need check ( for each tenant )
            $match["$or"].push({ user_id: tenant_customer_id });
        }

        const pipeline = [{ $match }];

        pipeline.push({
            $lookup: {
                from: "roles",
                localField: "role_id",
                foreignField: "_id",
                as: "role_id"
            }
        },
            {
                $lookup: {
                    from: "tenants",
                    localField: "tenant_id",
                    foreignField: "_id",
                    as: "tenant_id"
                },
            },
            {
                $lookup: {
                    from: "tenant_branches",
                    localField: "branch_id",
                    foreignField: "_id",
                    as: "branch_id"
                },
            },
            {
                $addFields: {
                    role_id: { $first: "$role_id" },
                    tenant_id: { $first: "$tenant_id" },
                    branch_id: { $first: "$branch_id" },
                    country: { $first: "$country" },
                }
            },
            {
                $lookup: {
                    from: "tenant_app_settings",
                    localField: "tenant_id._id",
                    foreignField: "tenant_id",
                    as: "tenant_app_settings",
                    pipeline: [
                        {
                            $project: {
                                preferred_language: 1,
                                price_change: 1,
                                hide_out_of_stock_product: 1,
                                catalog_mode: 1
                            }
                        }
                    ]
                },
            },
            {
                $lookup: {
                    from: "countries",
                    localField: "tenant_id.country",
                    foreignField: "_id",
                    as: "country",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                timezone: 1,
                                currency: 1,
                                country_code: 1,
                                secondary_language_name: 1,
                                secondary_language_code: 1,
                            }
                        }
                    ]
                },
            },
            {
                $addFields: {
                    country: { $first: "$country" },
                    tenant_app_settings: { $first: "$tenant_app_settings" },
                }
            },
        );

        if (getSalesPersonName) {
            pipeline.push(
                {
                    $lookup: {
                        from: "user_roles",
                        localField: "sales_person_id",
                        foreignField: "_id",
                        as: "salesperson",
                        pipeline: [
                            {
                                $project: {
                                    user_id: 1,
                                    branch_id: 1,
                                }
                            }
                        ]
                    }
                },
                {
                    $addFields: {
                        salesperson: { $first: "$salesperson" }
                    }
                },
                {
                    $lookup: {
                        from: "tenant_branches",
                        localField: "salesperson.branch_id",
                        foreignField: "_id",
                        as: "salesPersonBranch"
                    }
                },
                {
                    $addFields: {
                        salesPersonBranch: { $first: "$salesPersonBranch" }
                    }
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "salesperson.user_id",
                        foreignField: "_id",
                        as: "salesperson",
                        pipeline: [
                            {
                                $project: {
                                    first_name: 1,
                                    last_name: 1,
                                }
                            }
                        ]
                    }
                },
                {
                    $lookup: {
                        from: "tenant_branches",
                        localField: "tenant_id._id",
                        foreignField: "tenant_id",
                        as: "defaultBranch",
                        pipeline: [
                            {
                                $match: {
                                    is_default: true
                                },
                            },
                            {
                                $limit: 1
                            },
                            {
                                $project: {
                                    _id: 1,
                                }
                            }
                        ]
                    }
                },
                {
                    $addFields: {
                        salesperson: { $first: "$salesperson" },
                        defaultBranch: { $first: "$defaultBranch" }
                    }
                },
            )
        }

        pipeline.push(
            {
                $project: {
                    role_id: {
                        _id: "$role_id._id",
                        name: "$role_id.name",
                        portal_type: "$role_id.portal_type",
                        description: "$role_id.description",
                        permission: "$role_id.permission",
                    },
                    tenant_id: {
                        _id: "$tenant_id._id",
                        name: "$tenant_id.name",
                        services: "$tenant_id.services",
                        is_active: "$tenant.is_active",
                        subscription_start_date: "$tenant_id.subscription_start_date",
                        subscription_end_date: "$tenant_id.subscription_end_date",
                        country: "$country",
                        preferred_language: "$tenant_app_settings.preferred_language",
                        tenant_app_settings: "$tenant_app_settings",
                        linked_tenants: "$tenant_id.linked_tenant_id"
                    },
                    branch_id: {
                        $switch: {
                            branches: [
                                { case: { $eq: [{ $toBool: "$branch_id" }, true] }, then: "$branch_id" },
                                { case: { $eq: [{ $toBool: "$salesPersonBranch" }, true] }, then: "$salesPersonBranch" }
                            ],
                            default: "$branch_id"
                        }
                    },
                    user_id: 1,
                    collection_name: 1, // Do not remove it. It used in user_session for collection_name reference
                    supervisor_id: 1,
                    is_active: 1,
                    salesperson: 1,
                    price_list_id: 1,
                    settings: 1,
                    customer_last_name: 1,
                    customer_first_name: 1,
                    customer_name: 1,
                    external_id: 1,
                    customer_legal_name: 1,
                    customer_email: 1,
                    device_access: 1,
                    allow_price_change: 1,
                    price_list_id: 1,
                    customer_app_access: 1,
                    customer_catalog_mode: 1,
                    sales_person_id: 1,
                    defaultBranch: "$defaultBranch._id",
                    // tenant_app_settings: 1
                }
            },
            {
                // fetching linked tenant information for Tenant Owner and Tenant Admin profiles
                $lookup: {
                    from: "tenants",
                    localField: "tenant_id.linked_tenants",
                    foreignField: "_id",
                    as: "tenant_id.linked_tenants",
                    let: { portal_type: "$role_id.portal_type" },
                    pipeline: [
                        {
                            $match: {
                                is_active: true,
                                is_deleted: false,
                                $expr: {
                                    $eq: ["$$portal_type", VALUES.portals.TENANT_PORTAL]
                                }
                            }
                        },
                        {
                            $lookup: {
                                from: "tenant_app_settings",
                                localField: "_id",
                                foreignField: "tenant_id",
                                as: "tenant_app_settings",
                                pipeline: [
                                    {
                                        $project: {
                                            preferred_language: 1,
                                            price_change: 1,
                                            hide_out_of_stock_product: 1,
                                            catalog_mode: 1
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: "countries",
                                localField: "country_id",
                                foreignField: "id",
                                as: "country",
                                pipeline: [
                                    {
                                        $project: {
                                            name: 1,
                                            timezone: 1,
                                            country_code: 1,
                                            secondary_language_name: 1,
                                            secondary_language_code: 1,
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $lookup: {
                                from: "tenant_branches",
                                localField: "_id",
                                foreignField: "tenant_id",
                                as: "defaultBranch",
                                pipeline: [
                                    {
                                        $match: {
                                            is_default: true
                                        },
                                    },
                                    {
                                        $limit: 1
                                    },
                                    {
                                        $project: {
                                            _id: 1,
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            $addFields: {
                                tenant_app_settings: { $first: "$tenant_app_settings" },
                                country: { $first: "$country" },
                                defaultBranch: { $first: "$defaultBranch" }
                            }
                        },
                        {
                            $project: {
                                name: 1,
                                subscription_start_date: 1,
                                subscription_end_date: 1,
                                country: 1,
                                preferred_language: "$tenant_app_settings.preferred_language",
                                tenant_app_settings: "$tenant_app_settings",
                                is_active: 1,
                                defaultBranch: "$defaultBranch._id"

                            }
                        },

                    ]
                }
            },
        )

        return UserRoleSchema.aggregate(pipeline);
    }

    async adminInitAUthForAppUser(UserName) {
        const params = {
            UserPoolId: process.env.AWS_USER_POOL_ID,
            ClientId: process.env.AWS_USER_POOL_CLIENT_ID,
            AuthFlow: "CUSTOM_AUTH",
            AuthParameters: {
                USERNAME: UserName,
            }
        }
        return new Promise((resolve, reject) => {
            AWSAdminCognito.adminInitiateAuth(params, function (err, result) {
                if (err) {
                    return reject(err);
                }
                resolve(result);
            })
        })
    }

    async verifyAppOtpTokenForAuth(body) {
        const params = {
            UserPoolId: process.env.AWS_USER_POOL_ID,
            ClientId: process.env.AWS_USER_POOL_CLIENT_ID,
            ChallengeName: "CUSTOM_CHALLENGE",
            Session: body.session,
            ChallengeResponses: {
                USERNAME: body.username,
                ANSWER: body.otp
            }
        }
        return new Promise((resolve, reject) => {
            AWSAdminCognito.adminRespondToAuthChallenge(params, function (err, result) {
                if (err) {
                    return reject(err);
                }

                resolve(result);
            })
        })
    }



    getCognitoUser(emailOrPhNumber) {
        const userData = {
            Username: emailOrPhNumber,
            Pool: userPool
        };
        return new AmazonCognitoIdentity.CognitoUser(userData);
    }

    async getUserSessionFromAccessToken(access_token) {
        return UserSessionSchema.findOne({ access_token });
    }


}

module.exports = AuthModal
