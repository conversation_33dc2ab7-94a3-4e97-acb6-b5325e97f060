const difference = require("lodash.difference")

const { sendEmail } = require("../Configs/mailer")
const { EMAIL_ID, VALUES } = require("../Configs/constants")

class DataSheetModel {

    static getCcEmailIds = (userEmail) => {
        return difference([EMAIL_ID.FORAM], [userEmail])
    }

    sendDataSheetEmail = (userEmail, subject, html) => {
        const ccEmailIds = DataSheetModel.getCcEmailIds(userEmail)

        const emailMetadata = [{
            "receiver": userEmail,
            subject,
        }]

        if (VALUES.IS_PROD_ENV && ccEmailIds.length) {
            emailMetadata.push({
                "receiver": ccEmailIds[0],
                "subject": (subject + ` - ${userEmail}`),
                "cc": ccEmailIds.slice(1),
            })
        }

        return Promise.all(
            emailMetadata.map(metadata => {
                const {
                    receiver,
                    subject,
                    cc,
                } = metadata

                return sendEmail(
                    receiver,
                    subject,
                    {
                        html
                    },
                    cc,
                    EMAIL_ID.NOTIFICATION,
                )
            })
        )
    }

    sendSuccessProcessEmail = async (req, otherInfo = {}) => {
        const {
            tenantId,
            dataType,
            operationType,
        } = req.body

        const userEmail = req.headers.userDetails.email

        const {
            excelFile,
        } = otherInfo || {}

        const subject = "Success: Data Sheet Process"
        const html = `
                Data sheet for export processed successfully.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenantId}
                <br/><br/>

                <b> Data Type: </b> ${dataType}
                <br/><br/>

                <b> Operation Type: </b> ${operationType}
                <br/><br/>

                <b> Excel Sheet URL: </b> ${excelFile}
                `

        await this.sendDataSheetEmail(userEmail, subject, html)
    }

    sendValidationSuccessProcessEmail = async (req, otherInfo = {}) => {
        const {
            tenantId,
            fileId,
            approveType,
            selectRow,
        } = req.body

        const userEmail = req.headers.userDetails.email

        const {
            dataType,
            operationType,
            fileName,
        } = otherInfo || {}

        const subject = "Success: Data Sheet Process"
        const html = `
                Data sheet processed successfully.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenantId}
                <br/><br/>

                <b> Data Type: </b> ${dataType}
                <br/><br/>

                <b> Operation Type: </b> ${operationType}
                <br/><br/>

                <b> File Id: </b> ${fileId}
                <br/><br/>

                <b> File Name: </b> ${fileName}
                <br/><br/>

                <b> Approve Type: </b> ${approveType}
                <br/><br/>

                <b> Row Numbers: </b> ${selectRow ? selectRow.join(", ") : "N/A"}
                `

        await this.sendDataSheetEmail(userEmail, subject, html)
    }

    sendValidationFailProcessEmail = async (req, errors) => {
        const {
            tenantId,
            fileId,
            approveType,
            selectRow,
        } = req.body

        const userEmail = req.headers.userDetails.email
        const subject = "Failed: Data Sheet Process"
        const html = `
                Unable to process the data sheet.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenantId}
                <br/><br/>

                <b> File Id: </b> ${fileId}
                <br/><br/>

                <b> Approve Type: </b> ${approveType}
                <br/><br/>

                <b> Row Numbers: </b> ${selectRow ? selectRow.join(", ") : "N/A"}
                <br/><br/>

                <b> Reason: </b> ${errors}
                `

        await this.sendDataSheetEmail(userEmail, subject, html)
    }

    sendExportFailEmail = async (req, errors) => {
        const {
            tenantId,
            dataType,
            operationType,
        } = req.body

        const userEmail = req.headers.userDetails.email
        const subject = "Failed: Data Sheet Process"
        const html = `
                Unable to generate an excel file for export.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenantId}
                <br/><br/>

                <b> Data Type: </b> ${dataType}
                <br/><br/>

                <b> Operation Type: </b> ${operationType}
                <br/><br/>

                <b> Reason: </b> ${errors}
                `

        await this.sendDataSheetEmail(userEmail, subject, html)
    }

    sendUploadSuccessEmail = async (req) => {
        let {
            tenantId,
            dataType,
            operationType,
            fileName,
            originalFileName,
        } = req.body

        tenantId = tenantId || req.headers.userDetails.tenant_id || "N/A"
        fileName = originalFileName || fileName

        const userEmail = req.headers.userDetails.email
        const subject = "Success: Data Sheet Upload"
        const html = `
                Data sheet uploaded successfully.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenantId}
                <br/><br/>

                <b> Data Type: </b> ${dataType}
                <br/><br/>

                <b> Operation Type: </b> ${operationType}
                <br/><br/>

                <b> File Name: </b> ${fileName}
                `

        await this.sendDataSheetEmail(userEmail, subject, html)
    }

    sendUploadFailEmail = async (req, errors) => {
        let {
            tenantId,
            dataType,
            operationType,
            fileName,
            originalFileName,
        } = req.body

        tenantId = tenantId || req.headers.userDetails.tenant_id || "N/A"
        fileName = originalFileName || fileName

        const userEmail = req.headers.userDetails.email
        const subject = "Failed: Data Sheet Upload"
        const html = `
                Unable to update the uploaded file.
                <br/><br/>

                <b> Environment: </b> ${VALUES.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${tenantId}
                <br/><br/>

                <b> Data Type: </b> ${dataType}
                <br/><br/>

                <b> Operation Type: </b> ${operationType}
                <br/><br/>

                <b> File Name: </b> ${fileName}
                <br/><br/>

                <b> Reason: </b> ${errors}
                `

        await this.sendDataSheetEmail(userEmail, subject, html)
    }
}

module.exports = DataSheetModel
