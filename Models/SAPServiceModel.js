const axios = require("axios")
const moment = require("moment")

const EncryptionHandler = new (require("../Configs/encrypt"))();

const {
    SAP_SERVICE,
    STATUS_CODES
} = require("../Configs/constants")

module.exports = class {

    #callSAPApi = async (
        path,
        params,
        sapIntegrationCredentials,
    ) => {
        try {
            const decryptedPassword = EncryptionHandler.decrypt(sapIntegrationCredentials.configurations.password);
            const {
                status = "",
                data = "",
                error,
            } = decryptedPassword || {};

            if (status === STATUS_CODES.SERVER_ERROR) {
                throw {
                    name: SAP_SERVICE.ERROR.API,
                    message: "unable_to_parse_sap_password",
                    statuscode: STATUS_CODES.BAD_REQUEST,
                    data: error.response?.data,
                    ...error
                }
            }
            else {
                return await axios.post(
                    sapIntegrationCredentials.configurations.base_url + "/" + path,
                    params,
                    {
                        auth: {
                            username: sapIntegrationCredentials.configurations.username,
                            password: data,
                        }
                    }
                )
            }
        }
        catch (error) {
            throw {
                name: SAP_SERVICE.ERROR.API,
                message: "sap_service_api_error",
                statuscode: STATUS_CODES.BAD_REQUEST,
                ...error
            }
        }
    }

    getBalance = async (
        query,
        sapIntegrationCredentials,
    ) => {
        const {
            customerExternalId,
            agingDate
        } = query

        const response = await this.#callSAPApi(
            "bpAging",
            {
                bpCode: customerExternalId,
                agingDate
            },
            sapIntegrationCredentials,
        )

        let balanceData

        if (typeof response?.data === "object") {
            balanceData = {
                customer_external_id: response.data.CardCode,
                customer_legal_name: response.data.CardName,
                balance_0_30_days: response.data.Balance_0_30_days,
                balance_31_60_days: response.data.Balance_31_60_days,
                balance_61_90_days: response.data.Balance_61_90_days,
                balance_91_120_days: response.data.Balance_91_120_days,
                balance_120_days_above: response.data.Balance_120_days_above,
                balance_due: response.data.BalanceDue,
                current_balance: response.data.CurrentBalance,
            }
        }

        return balanceData
    }

    getStatements = async (
        query,
        sapIntegrationCredentials,
    ) => {
        const {
            customerExternalId,
            fromDate,
            toDate,
            page,
            perPage
        } = query

        const response = await this.#callSAPApi(
            "bpStatement",
            {
                bpCode: customerExternalId,
                fromDate,
                toDate
            },
            sapIntegrationCredentials,
        )

        let data
        if (typeof response?.data === "object") {
            data = {
                customer_external_id: response.data.CardCode,
                customer_legal_name: response.data.CardName,
                opening_balance: response.data.OpeningBalance,
                statements: response
                    .data
                    .Documents
                    .map(doc => {
                        return {
                            document_number: doc.DocNum,
                            document_type: doc.DocType,
                            post_date: doc.PostDate,
                            remarks: doc.JERemarks,
                            debit_amount: doc.DebitAmount,
                            credit_amount: doc.CreditAmount,
                            credit_memo_type: doc.CreditMemoType,
                        }
                    })
                    .sort((a, b) => {
                        return new Date(b.post_date) - new Date(a.post_date)
                    }),
            }
        }

        if (perPage && page && data) {
            const offset = perPage * (page - 1);
            data.statementCount = data.statements.length
            data.statements = data.statements.slice(offset, offset + perPage)
        }

        return data
    }

    generateStatementsSummery = (statements) => {
        return statements.reduce((obj, statement) => {
            const date = moment(statement.post_date).format("YYYY/MM/DD")

            let amountObj

            if (!obj[date])
                obj[date] = {}

            if (!obj[date][statement.document_type])
                obj[date][statement.document_type] = {}

            amountObj = obj[date][statement.document_type]            

            if (statement.credit_memo_type) {
                if (!amountObj[statement.credit_memo_type])
                    amountObj[statement.credit_memo_type] = {}

                amountObj = amountObj[statement.credit_memo_type]
            }

            if (!amountObj.document_numbers) {
                amountObj.document_numbers = []
            }

            amountObj.debit_amount = (amountObj.debit_amount ?? 0) + statement.debit_amount
            amountObj.credit_amount = (amountObj.credit_amount ?? 0) + statement.credit_amount
            
            // Add document number to the array
            if (statement.document_number) {
                amountObj.document_numbers.push(statement.document_number)
            }
            
            return obj
        }, {})
    }

    checkPaymentsOnTime = (numberOfDays, aging) => {
        const {
            balance_61_90_days,
            balance_91_120_days,
            balance_120_days_above
        } = aging

        let totalAging = 0

        if (numberOfDays <= 60) {
            totalAging += balance_61_90_days
        }

        if (numberOfDays <= 90) {
            totalAging += balance_91_120_days
        }

        if (numberOfDays <= 120) {
            totalAging += balance_120_days_above
        }

        return totalAging <= 100
    }

    checkPreApproved = async (
        externalId,
        tenantId,
        sapIntegrationCredentials
    ) => {
        if (!externalId || !tenantId)
            return false

        const toDate = moment().subtract(90, 'days')
        const fromDate = moment(toDate).subtract(1, 'years')

        const statements = await this.getStatements(
            {
                customerExternalId: externalId,
                toDate: parseInt(toDate.format("YYYYMMDD")),
                fromDate: parseInt(fromDate.format("YYYYMMDD")),
            },
            sapIntegrationCredentials
        )

        const haveInvoice = statements?.statements?.some((document) => {
            return document.document_type === SAP_SERVICE.DOCUMENT_TYPE.INVOICE
        }) ?? false

        if (!haveInvoice) {
            return false
        }

        const aging = await this.getBalance(
            {
                customerExternalId: externalId,
                agingDate: parseInt(moment().format("YYYYMMDD"))
            },
            sapIntegrationCredentials
        )

        if (!aging)
            return false;

        return this.checkPaymentsOnTime(90, aging)
    }
    
    getRecentlyRestockedItems = async (sapIntegrationCredentials) => {
        const response = await this.#callSAPApi(
            "grpo30",
            {
                "OnHand": 50
            },
            sapIntegrationCredentials,
        )

        let data

        if (typeof response?.data === "object") {
            data = response?.data.map(items => {
                return items.ItemCode
            })
        }

        return data
    }

}
