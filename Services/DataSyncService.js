const TenantPortalModel = new (require('../Models/tenantPortal'))()
const RoleModel = new (require('../Models/roles'))()
const BaseDataSyncService = require('./BaseDataSyncService');

const {
    toLeanOption,
} = require('../Utils/helpers');

class DataSyncService extends BaseDataSyncService {

    async getTenantAppSetting(query) {
        const {
            tenantId,
        } = query;

        // Get tenant app setting (only one per tenant)
        const tenantAppSetting = await TenantPortalModel.tenantAppSettingExist(tenantId, "-__v", toLeanOption);
        return tenantAppSetting;
    }

    async getUserRoleSettings(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = query

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => TenantPortalModel.getUserRoleSettingsCount(filter),
            findFunction: (filter, selectFields, options) => TenantPortalModel.findUserRoleSettings(filter, selectFields, options),
            selectFields: "-__v",
            leanOptions: toLeanOption
        });
    }

    async updateUserRoleSettings(body) {
        try {
            const {
                tenant_id,
                user_role_id,
                default_master_price_id,
                out_of_stock,
                preferred_language,
                price_change,
                updated_at
            } = body;

            // Validate required fields
            if (!tenant_id || !user_role_id) {
                throw new Error('tenant_id and user_role_id are required');
            }

            if (!updated_at) {
                throw new Error('updated_at timestamp is required for comparison');
            }

            // Check if existing data has a more recent updated_at timestamp
            const existingSettings = await TenantPortalModel.findUserRoleSetting(
                { _id: `${tenant_id}_${user_role_id}` },
                { created_at: 0, __v: 0 }
            );
            const existingTimestamp = existingSettings ? new Date(existingSettings.updated_at).getTime() : 0;
            const incomingTimestamp = new Date(updated_at).getTime();

            // If existing data is more recent, skip the update
            if (existingTimestamp > incomingTimestamp) {
                return {
                    success: true,
                    isUpToDate: false,
                    message: "Update skipped - existing data is more recent",
                    existingData: [existingSettings],
                };
            }
            

            // Prepare update object with only provided fields
            const updateObject = {};
            if (default_master_price_id !== undefined) updateObject.default_master_price_id = default_master_price_id;
            if (out_of_stock !== undefined) updateObject.out_of_stock = out_of_stock;
            if (preferred_language !== undefined) updateObject.preferred_language = preferred_language;
            if (price_change !== undefined) updateObject.price_change = price_change;
            if (updated_at !== undefined) updateObject.updated_at = updated_at;

            // Ensure we have at least one field to update
            if (Object.keys(updateObject).length === 0) {
                return {
                    success: false,
                    message: "No fields to update",
                };
            }
            if (existingSettings && existingTimestamp == incomingTimestamp) {
                return {
                    success: true,
                    isUpToDate: true,
                    existingData: [],
                    message: "User role settings updated successfully1"
                };
            }

            // Update the user role settings
            await TenantPortalModel.updateUserRoleSetting(
                { _id: `${tenant_id}_${user_role_id}` },
                updateObject,
                { upsert: true, new: true , timestamps: false }
            );

            return {
                success: true,
                isUpToDate: true,
                existingData: [],
                message: "User role settings updated successfully"
            };

        } catch (error) {
            console.error('Error updating user role settings:', error);
            throw error;
        }
    }

    async getUserRoles(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            isInitialSync,
            roles,
            perPage = 50,
        } = query

        const filter = {
            tenant_id: tenantId,
        };

        // Only apply is_deleted and is_active filters for initial sync
        if (isInitialSync) {
            filter.is_deleted = false;
            filter.is_active = true;
        }

        const roleIds = await RoleModel.getRolesWithFilter(
            {
                name: { $in: roles },
                is_deleted: false,
                is_active: true,
            },
            { _id: 1, },
            toLeanOption,
        );
        filter.role_id = { $in: roleIds.map(role => new mongoose.Types.ObjectId(role._id)) };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => TenantPortalModel.getTenantUsersCount(filter),
            findFunction: (filter, selectFields, options) => TenantPortalModel.getTenantUsers(filter, selectFields, options),
            selectFields: `
                collection_name
                customer_app_access
                customer_app_request
                customer_email
                customer_first_name
                customer_id
                customer_last_name
                customer_legal_name
                customer_name
                external_id
                branch_id
                is_active
                is_deleted
                is_verified
                allow_price_change
                preferred_language
                price_list_id
                role_id
                sales_person_id
                tenant_id
                user_id
                gps_coordinates
                shipping_address
                shipping_city_id
                shipping_country_code
                shipping_country_id
                shipping_region_id
                shipping_mobile_number
                created_at
                updated_at
                `,
            leanOptions: {
                ...toLeanOption,
                populate: [{
                    path: "user_id",
                    select: "first_name last_name email mobile_number country_code"
                }, {
                    path: "sales_person_id",
                    select: "-__v -created_by -updated_by"
                }, {
                    path: "shipping_region_id",
                    select: "-__v -created_by -updated_by"
                }, {
                    path: "shipping_city_id",
                    select: "-__v -created_by -updated_by"
                }]
            }
        });
    }

}

module.exports = new DataSyncService(); 
