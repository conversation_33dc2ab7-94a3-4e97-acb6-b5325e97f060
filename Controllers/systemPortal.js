const SystemPortalModel = new (require('../Models/systemPortal'))();
const TenantPortalModal = new (require("../Models/tenantPortal"))();
const HoldReasonModel = new (require('../Models/HoldReasonModel'))();
const RoleModal = new (require('../Models/roles'));
const UserAuthModal = new (require('../Models/auth'))();

const CommonController = new (require('./common/common'))();

const encrypt = new (require("../Configs/encrypt"))();
const FileUpload = require('../Configs/awsUploader').S3Upload;
const fileUpload = new FileUpload();

const {
    generateOtp,
    httpService,
    toLeanOption,
} = require('../Utils/helpers');

const {
    VALUES,
    SMS_PROVIDER_INFO,
    FILE_PATH,
    ENTITY_STATUS,
    PRIMITIVE_ROLES,
    SALES_TYPE,
    TENANT_QUERY_TYPE_ENUM,
    BULK_CREATE_HOLD_REASONS,
    LANGUAGE
} = require('../Configs/constants');

class SystemPortalController {
    async getTenantDefaults(req, res) {
        try {
            const defaults = {};
            const promises = [];
            promises.push(SystemPortalModel.getTenantDefaultNotificationType());
            promises.push(SystemPortalModel.getTenantDefaultServices())
            promises.push(SystemPortalModel.getTenantDefaultAdvanceLimits());
            const [notificationTypes, tenantServices, advanceLimit] = await Promise.all(promises);

            defaults['notificationTypes'] = notificationTypes;
            defaults['tenantServices'] = tenantServices;
            defaults['advanceLimit'] = advanceLimit;
            return res.handler.success(null, defaults);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addTenantShippingLabel(tenant, tenantBranches, headers) {
        try {
            const { _id: tenantId, street_name, region, city } = tenant
            const { _id: branch_id, name: branchName } = tenantBranches[0]

            const options = { lean: true }

            const populationArray = [
                {
                    path: "primary_contact_id",
                    select: "-_id country_code mobile_number"
                }
            ]
            const projection = "primary_contact_id"
            const tenantInfo = await SystemPortalModel.getTenantsById(tenantId, projection, options, populationArray)

            const shippingLabelBody = {
                "tenant_id": tenantId,
                "tenant_legal_name": tenant.legal_name,
                "branches": [{
                    branch_id,
                    "name": branchName,
                    "street_address": street_name,
                    "region": region,
                    "city": city,
                    "mobile_number": tenantInfo?.primary_contact_id?.mobile_number,
                    "mobile_number_country_code": tenantInfo?.primary_contact_id?.country_code,
                }],
            }

            const shippingLabel = await TenantPortalModal.addShippingLabel(shippingLabelBody, headers)
            shippingLabel.save()
        }
        catch (error) {
            logger.error(error)
        }
    }

    addTenant = async (req, res) => {
        try {
            if (!req.body.details?.firstName || !req.body.details?.lastName || !req.body.details?.email || !req.body.details?.mobileNumber || !req.body.details?.countryCode) {
                return res.handler.validationError("validation_invalid_primary_contact_details")
            }
            const userWithMobileNumber = await UserAuthModal.findUserByMobileNumber(req.body.details.mobileNumber, req.body.details.countryCode);
            if (!userWithMobileNumber) {
                const userExists = await UserAuthModal.findUserByEmail(req.body.details.email);
                // if mobile number does not exists and email does, we will throw validation error
                if (userExists) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                }
                const userDetails = await UserAuthModal.signup(req.body.details);

                req.body.details.password = "66G1!d8LviS02x@5i$7" // TODO: can generate random password, every time  where we add a user
                const cognitoData = await UserAuthModal.creatUserInUserPool(req.body.details);
                userDetails.cognito_username = cognitoData.userSub;
                const otp = generateOtp();
                userDetails.forgot_password_otp = otp;
                userDetails.otp_verification_time = new Date();
                userDetails.otp_verification_time_limit = VALUES.PASSWORD_REST_OR_CREATE_DURATION;
                await userDetails.save();
                await UserAuthModal.sendWelcomePortalUser(userDetails);
                // await UserAuthModal.sendEMailVerificationLink(req.body.details.email, otp, userDetails._id.toString() ,"Verification OTP");
                await UserAuthModal.sendOtpVerificationEMail({ username: `${userDetails.first_name} ${userDetails.last_name}`, otp_num: otp, rtl: false }, undefined, userDetails.email);

                await Promise.all([UserAuthModal.adminConfirmSignUp(req.body.details), UserAuthModal.verifyUserDetails(req.body.details)]);
                req.body.details.primaryContactId = userDetails._id;
            } else {
                // if new anything from firstName, lastName and email is changed for the user, it will be changed for the userWithMobileNumber record
                if (userWithMobileNumber.email !== req.body.details?.email) {
                    const userExists = await UserAuthModal.findUserByEmail(req.body.details.email);
                    // if the email id is colliding with other user, then throw error
                    if (userExists && userExists._id.toString() !== userWithMobileNumber._id.toString()) {
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                    }
                }
                const updateUserDetails = {
                    firstName: req.body.details?.firstName,
                    lastName: req.body.details?.lastName,
                    previousEmail: userWithMobileNumber.email,
                    email: req.body.details?.email
                }
                const otherTenantProfile = await UserAuthModal.getUserRoleWithPopulation(
                    { tenant_id: { $ne: null }, is_deleted: false, user_id: userWithMobileNumber._id },
                    { _id: 1 },
                );
                if (otherTenantProfile) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, "validation_exists_user");
                }
                await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                userWithMobileNumber.email = req.body.details?.email;
                userWithMobileNumber.first_name = req.body.details?.firstName;
                userWithMobileNumber.last_name = req.body.details?.lastName;
                userWithMobileNumber.updated_by = req.headers.userDetails._id;
                await userWithMobileNumber.save();
                req.body.details.primaryContactId = userWithMobileNumber._id;
            }

            const tenant = await SystemPortalModel.createNewTenant(req.body, req.headers);
            if (req.body.services?.hasCustomSenderId) {
                if (!req.body.services?.smsUserName || !req.body.services?.smsPassword || !req.body.services?.smsSenderId) {
                    return res.handler.badRequest("validation_invalid_sms_credentials");
                }
                //TODO: update code here when we need to integrate new sms provider
                tenant.custom_sms_details = {
                    type: SMS_PROVIDER_INFO.UNIFONIC,
                    unifonic_credentials: {
                        username: req.body.services?.smsUserName,
                        password: req.body.services?.smsPassword,
                        sender_id: req.body.services?.smsSenderId
                    }
                }
            }
            await tenant.save();

            const configurationAppSetting = await TenantPortalModal.configurationAppSetting(tenant._id, req.headers);
            await configurationAppSetting.save();

            await Promise.all([
                SystemPortalModel.defaultTaxMaster(tenant._id, req),
                SystemPortalModel.defaultVisitConfiguration(tenant._id, req)
            ])

            // assign user (primary contact user) tenant owner role
            const tenantOwnerRole = await SystemPortalModel.getTenantOwnerRole();

            const userRole = await SystemPortalModel.assignTenantOwnerRole(
                req.body.details.primaryContactId,
                tenantOwnerRole._id,
                tenant._id,
                req.body.isActive || false,
                undefined,
                req.headers
            )
            const settings = {
                out_of_stock: {
                    visible: false,
                    searchable: false,
                },
                price_change: false,
                preferred_language: LANGUAGE.EN,
            }
            const userRoleSettings = await TenantPortalModal.createUserRoleSetting({
                _id: `${tenant._id}_${userRole._id}`,
                tenant_id: tenant._id,
                user_role_id: userRole._id,
                ...settings
            })
            await userRoleSettings.save();
            const tenantBranches = await SystemPortalModel.addTenantBranches(tenant, req.headers, req.body.branches);
            await Promise.all([userRole.save(), SystemPortalModel.addInitWareHouses(tenant._id, tenantBranches.map(tb => tb._id), req.headers)])
            tenant.last_branch_id = req.body.branches?.length;
            await tenant.save();

            /** Add shipping label settings for the created tenant */
            await this.addTenantShippingLabel(tenant, tenantBranches, req.headers)

            const holdReasons = [];
            BULK_CREATE_HOLD_REASONS.forEach(holReason => {
                holdReasons.push({
                    ...holReason,
                    tenant_id: tenant._id,
                    created_by: req.headers.userDetails?._id,
                    updated_by: req.headers.userDetails?._id
                })
            })

            await HoldReasonModel.createHoldReasons(holdReasons);

            return res.handler.success(null, tenant);

        } catch (error) {
            switch (error.name) {
                case "ValidationError":
                    return res.handler.validationError(undefined, error)

                default:
                    return res.handler.serverError(error);
            }
        }
    }

    async updateTenant(req, res) {
        try {

            if (!req.body.details?.firstName || !req.body.details?.lastName || !req.body.details?.email || !req.body.details?.mobileNumber || !req.body.details?.countryCode) {
                return res.handler.validationError("validation_invalid_primary_contact_details")
            }
            const tenantDetails = await SystemPortalModel.getTenantWithOwner(req.query.tenantId);
            if (!tenantDetails || tenantDetails.is_deleted) {
                return res.handler.notFound();
            }

            // if owner is changed
            if ((req.body.details.countryCode + req.body.details.mobileNumber) !== (tenantDetails.primary_contact_id.country_code + tenantDetails.primary_contact_id.mobile_number)) {
                const newOwner = await UserAuthModal.findUserByMobileNumber(req.body.details.mobileNumber, req.body.details.countryCode);
                // if the provided number linked user not found, create new user
                if (!newOwner) {
                    const existingUserWithEmail = await UserAuthModal.findUserByEmail(req.body.details?.email);
                    // check for email collision with other user.
                    if (existingUserWithEmail) {
                        // should not update mobile number of the user, that is why we have not handler the else case.
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                    }
                    const userDetails = await UserAuthModal.signup(req.body.details);

                    req.body.details.password = "66G1!d8LviS02x@5i$7"
                    const cognitoData = await UserAuthModal.creatUserInUserPool(req.body.details);
                    userDetails.cognito_username = cognitoData.userSub;
                    const otp = generateOtp();
                    userDetails.forgot_password_otp = otp;
                    userDetails.otp_verification_time = new Date();
                    userDetails.otp_verification_time_limit = VALUES.PASSWORD_REST_OR_CREATE_DURATION;
                    await userDetails.save();
                    // await UserAuthModal.sendEMailVerificationLink(req.body.details.email, otp, userDetails._id.toString() ,"Verification OTP");
                    await UserAuthModal.sendOtpVerificationEMail({ username: `${userDetails.first_name} ${userDetails.last_name}`, otp_num: otp, rtl: false }, undefined, userDetails.email);


                    await Promise.all([UserAuthModal.adminConfirmSignUp(req.body.details), UserAuthModal.verifyUserDetails(req.body.details)]);
                    req.body.details.primaryContactId = userDetails._id;
                    // create new user
                } else {
                    // if the linked user with provided mobileNumber is found, check for email collision with other user
                    const existingUser = await UserAuthModal.findUserByEmail(req.body.details?.email);
                    if (existingUser && existingUser._id.toString() !== newOwner._id.toString()) {
                        // means email already belongs to other user
                        return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                    }

                    const adminRole = await RoleModal.getRoleByFilter({ name: PRIMITIVE_ROLES.TENANT_ADMIN }, { _id: 1 });

                    // check user is already not having profile in any other tenant, or if user is already not admin in same tenant
                    const otherTenantProfile = await UserAuthModal.getUserRoleWithPopulation(
                        {
                            // $or: [
                            //     { $and: [{ tenant_id: { $ne: tenantDetails._id } }, { tenant_id: { $ne: null } }] },
                            //     { tenant_id: tenantDetails._id, role_id: adminRole._id }
                            // ],
                            is_deleted: false, user_id: existingUser._id,
                            tenant_id: { $ne: null } // user should not have any profile in the given tenant or any other tenant
                        },
                        { _id: 1 },
                    );

                    //Check if user is already not admin in same tenant
                    if (otherTenantProfile) {
                        return res.handler.custom(STATUS_CODES.CONFLICT, "validation_exists_user");
                    }
                    const updateUserDetails = {
                        firstName: req.body.details?.firstName,
                        lastName: req.body.details?.lastName,
                        previousEmail: newOwner.email,
                        email: req.body.details?.email
                    }
                    await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                    newOwner.email = req.body.details?.email;
                    newOwner.first_name = req.body.details?.firstName;
                    newOwner.last_name = req.body.details?.lastName;
                    newOwner.updated_by = req.headers.userDetails._id;
                    await newOwner.save();
                    req.body.details.primaryContactId = newOwner._id;
                }
            } else {
                // owner is not changed just name or email is changed
                const existingUser = await UserAuthModal.findUserByEmail(req.body.details?.email);
                if (existingUser && existingUser._id.toString() !== tenantDetails.primary_contact_id._id.toString()) {
                    // means email already belongs to other user
                    return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                }
                // change the current user's name and email according to body's details' object.
                const updateUserDetails = {
                    firstName: req.body.details?.firstName,
                    lastName: req.body.details?.lastName,
                    previousEmail: tenantDetails.primary_contact_id.email,
                    email: req.body.details?.email
                }
                await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                tenantDetails.primary_contact_id.email = req.body.details?.email;
                tenantDetails.primary_contact_id.first_name = req.body.details?.firstName;
                tenantDetails.primary_contact_id.last_name = req.body.details?.lastName;
                tenantDetails.primary_contact_id.updated_by = req.headers.userDetails._id;
                await tenantDetails.primary_contact_id.save();

                req.body.details.primaryContactId = tenantDetails.primary_contact_id._id;
            }

            if (tenantDetails.is_active !== (req.body.isActive || false)) {
                // TODO: need to update user role status by checking tenant's status
                await SystemPortalModel.updateTenantUserRolesStatusByTenantId(tenantDetails._id, req.body.isActive || false);
            }
            const body = req.body;

            tenantDetails.name = body.details.tenantName;
            tenantDetails.legal_name = body.details?.legalName;
            tenantDetails.street_name = body.details?.streetName;
            tenantDetails.country = body.details?.country;
            tenantDetails.timezone = body.details?.timezone;
            tenantDetails.region = body.details?.region;
            tenantDetails.city = body.details?.city;
            tenantDetails.country_code = body.details?.countryCode;
            tenantDetails.primary_contact_id = body.details?.primaryContactId;
            // tenantDetails.is_deleted = false;
            tenantDetails.services = body.services.serviceInfo;
            tenantDetails.enable_sms_services = body.services?.enableSMSServices || false;
            tenantDetails.has_custom_sender_id = body.services?.hasCustomSenderId || false;
            tenantDetails.last_branch_id = body.branches?.length;
            tenantDetails.advance_limit = body.advanced?.limits;
            tenantDetails.subscription_start_date = body.advanced?.subscription?.from;
            tenantDetails.subscription_end_date = body.advanced?.subscription?.to;
            tenantDetails.created_by = req.headers.userDetails._id;
            tenantDetails.updated_by = req.headers.userDetails._id;
            tenantDetails.is_active = req.body.isActive || false;

            if (req.body.services?.hasCustomSenderId) {
                if (!req.body.services?.smsUserName || !req.body.services?.smsPassword || !req.body.services?.smsSenderId) {
                    return res.handler.badRequest("validation_invalid_sms_credentials");
                }
                //TODO: update code here when we need to integrate new sms provider
                tenantDetails.custom_sms_details = {
                    type: SMS_PROVIDER_INFO.UNIFONIC,
                    unifonic_credentials: {
                        username: req.body.services?.smsUserName,
                        password: req.body.services?.smsPassword,
                        sender_id: req.body.services?.smsSenderId
                    }
                }
            } else if (req.body.services?.hasCustomSenderId === false) {
                tenantDetails.custom_sms_details = undefined
            }

            const detailsPromises = [tenantDetails.save()]
            const tenantOwnerRole = await SystemPortalModel.getTenantOwnerRole();
            const userTenantOwnerRole = await SystemPortalModel.getTenantOwnerProfile(tenantDetails._id, tenantOwnerRole._id)

            if (userTenantOwnerRole) {
                userTenantOwnerRole.user_id = tenantDetails.primary_contact_id;
                userTenantOwnerRole.is_active = req.body.isActive || false;
                userTenantOwnerRole.updated_by = req.headers.userDetails._id;
                userTenantOwnerRole.is_deleted = false; // make this role as non deleted if it was deleted when we deleted user.
                userTenantOwnerRole.collection_name = "users";
                detailsPromises.push(userTenantOwnerRole.save());
            }

            await Promise.all(detailsPromises);

            return res.handler.success("updated_tenant");
        } catch (error) {
            switch (error.name) {
                case "ValidationError":
                    return res.handler.validationError(undefined, error)

                default:
                    return res.handler.serverError(error);
            }
        }
    }

    async deleteTenant(req, res) {
        try {
            req.query.tenantId = parseInt(req.query.tenantId);
            const tenant = await SystemPortalModel.getTenantById(req.query.tenantId);
            if (!tenant) {
                return res.handler.notFound();
            }

            let [tenantUsersRole] = await Promise.all([
                SystemPortalModel.getTenantUsersById(tenant._id),
                SystemPortalModel.deleteTenantProfiles(tenant._id),
                SystemPortalModel.deleteTenantBranches(tenant._id),
            ]);

            tenantUsersRole = tenantUsersRole.map(userProfile => userProfile._id);
            await SystemPortalModel.expireTenantUserSessions(tenantUsersRole);

            tenant.is_active = false;
            tenant.is_deleted = true;
            tenant.updated_by = req.headers.userDetails._id;
            await tenant.save();

            return res.handler.success();

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getTenant(req, res) {
        try {
            const { tenantId, tenantQueryType, projection } = req.query;

            switch (tenantQueryType) {
                case TENANT_QUERY_TYPE_ENUM.QUERY_AND_PROJECTION:
                    {
                        const query = { _id: tenantId, is_deleted: false };
                        const tenantDetails = await SystemPortalModel.getTenantWithFilter(query, projection, { lean: true });
                        if (!tenantDetails) {
                            return res.handler.notFound('tenant_not_found');
                        }
                        return res.handler.success(null, tenantDetails)
                    }

                default:
                    {
                        const promises = [];
                        const tenantDetails = await SystemPortalModel.getTenantDetailsById(req.query.tenantId);
                        if (!tenantDetails || tenantDetails.is_deleted) {
                            return res.handler.notFound("tenant_not_found");
                        }
                        const tenantOwnerRole = await SystemPortalModel.getTenantOwnerRole();
                        //TODO: need to change logic for updating tenant notifications
                        promises.push(SystemPortalModel.getTenantOwnerNotifications(req.query.tenantId, tenantOwnerRole._id));
                        promises.push(SystemPortalModel.getTenantBranchesByTenantId(req.query.tenantId));
                        const [tenantNotifications, tenantBranches] = await Promise.all(promises);
                        return res.handler.success(null, { tenantDetails, tenantNotifications, tenantBranches });
                        break;
                    }
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getTenants(req, res) {
        try {
            const {
                perPage,
                page,
                searchKey,
            } = req.query;

            const offset = perPage * (page - 1);
            //TODO: need to change logic for account manager role's tenant listing API
            const data = await SystemPortalModel.getTenantsList(searchKey, offset, perPage, req.headers.sessionDetails);

            if (!data) {
                return res.handler.notFound();
            }

            return res.handler.success(null,
                {
                    list: data[0]?.tenants || [],
                    count: data[0]?.totalCount?.count || 0
                }
            );
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async searchTenant(req, res) {
        try {
            const tenants = await SystemPortalModel.searchTenants(req.query.searchKey);
            return res.handler.success(null, tenants);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addBranch(req, res) {
        try {
            const tenant = await SystemPortalModel.getTenantById(req.body.tenantId);
            if (!tenant.is_active) {
                return res.handler.badRequest("validation_in_active_tenant")
            }

            if (tenant.is_deleted) {
                return res.handler.badRequest("validation_deleted_tenant")
            }

            tenant.last_branch_id += 1;
            await tenant.save()
            const tenantBranch = await SystemPortalModel.AddTenantBranch(req.body, req.headers, tenant);
            tenantBranch.warehouse_counter += 1;
            await tenantBranch.save()
            const warehouse = await SystemPortalModel.addWareHouse(tenant._id, tenantBranch._id, "WH01", req.headers);
            await warehouse.save();
            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async editBranch(req, res) {
        try {
            const branch = await SystemPortalModel.getBranchById(req.body, req.headers);
            if (!branch) {
                return res.handler.notFound();
            }

            if (branch.tenant_id !== req.body.tenantId) {
                return res.handler.badRequest();
            }

            branch.name = req.body.name;
            branch.updated_by = req.headers.userDetails._id;
            await branch.save();
            return res.handler.success(null, branch);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteBranch(req, res) {
        try {
            const branch = await SystemPortalModel.getBranchById(req.query, req.headers);
            if (!branch) {
                return res.handler.notFound();
            }

            if (branch.tenant_id !== parseInt(req.query.tenantId)) {
                return res.handler.badRequest();
            }
            const branchUsers = await SystemPortalModel.getBranchUserRole(req.query.branchId);
            const promises = [];
            const userRoleIds = []
            for (let i = 0; i < branchUsers.length; i++) {
                const userRole = branchUsers[i];
                userRole.is_deleted = true;
                userRole.is_active = false;
                userRoleIds.push(userRoleIds._id);
                promises.push(userRole.save());
            }
            await Promise.all(promises);
            if (userRoleIds.length) {
                await SystemPortalModel.expireBranchUserSession(userRoleIds);
            }
            branch.is_active = false;
            branch.is_deleted = true;
            await branch.save();
            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addLinkedTenant(req, res) {
        try {
            const tenant = await SystemPortalModel.getTenantById(req.body.tenantId, { linked_tenant_id: 1, is_active: 1 });
            if (!tenant) {
                return res.handler.notFound();
            }
            if (!tenant.is_active) {
                return res.handler.badRequest("validation_in_active_tenant")
            }
            // const existingLinkedTenant = tenant.linked_tenant_id?.find(tId => tId === req.body.LinkingTenantId);
            // if(existingLinkedTenant !== undefined) {
            //     return res.handler.conflict("VALIDATION.EXISTS.LINKED_TENANT");
            // }

            // tenant.linked_tenant_id.push(req.body.LinkingTenantId);
            // await tenant.save();
            await SystemPortalModel.addLinkedTenant(req.body.tenantId, req.body.LinkingTenantId);

            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async removeLinkedTenant(req, res) {
        try {
            req.query.tenantId = parseInt(req.query.tenantId);
            req.query.LinkingTenantId = parseInt(req.query.LinkingTenantId);
            const tenant = await SystemPortalModel.getTenantById(req.query.tenantId, { linked_tenant_id: 1, is_active: 1 });
            if (!tenant) {
                return res.handler.notFound();
            }

            // const newLinkedTenants = tenant.linked_tenant_id.filter(tId => tId !== req.query.LinkingTenantId);
            // tenant.linked_tenant_id = newLinkedTenants;
            // await tenant.save();
            await SystemPortalModel.removeLinedTenant(req.query.tenantId, req.query.LinkingTenantId);
            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeTenantOwnerPassword(req, res) {
        try {
            const tenantDetails = await SystemPortalModel.getTenantWithOwner(req.body.tenantId);
            if (!tenantDetails) {
                return res.handler.notFound();
            }

            switch (req.body.type) {
                case VALUES.PASSWORD_RESET_TYPE.EMAIL_LINK:
                    const otp = encrypt.generateAuthToken()
                    // await UserAuthModal.sendEMailVerificationLink(tenantDetails.primary_contact_id.email, otp, tenantDetails.primary_contact_id._id.toString(), "Reset Password");
                    await UserAuthModal.resetPasswordLinkEmail(
                        {
                            username: `${tenantDetails.primary_contact_id.first_name}`,
                            reset_link: `${VALUES.WEB_APP_URL}auth/create-new-password?verifier=${otp}&id=${tenantDetails.primary_contact_id._id.toString()}`
                        },
                        tenantDetails.primary_contact_id.email
                    )
                    // await UserAuthModal.sendOtpVerificationEMail({ username: `${tenantDetails.primary_contact_id.first_name} ${tenantDetails.primary_contact_id.last_name}`, otp_num: otp, rtl: false }, undefined, tenantDetails.primary_contact_id.email, "Reset Password");
                    tenantDetails.primary_contact_id.forgot_password_otp = otp;
                    tenantDetails.primary_contact_id.otp_verification_time_limit = VALUES.PASSWORD_REST_OR_CREATE_DURATION;
                    tenantDetails.primary_contact_id.otp_verification_time = new Date();
                    await tenantDetails.primary_contact_id.save();
                    return res.handler.success("process_password_reset_link");

                case VALUES.PASSWORD_RESET_TYPE.MANUAL:
                    UserAuthModal.updateUserPassword(req.body.password, tenantDetails.primary_contact_id);
                    return res.handler.success("updated_password");
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getSystemUsers(req, res) {
        try {
            let systemPortalRoles = await RoleModal.getSystemPortalRoles();
            systemPortalRoles = systemPortalRoles.map(spr => spr._id);

            const users = await SystemPortalModel.getSystemUsers(req.query, systemPortalRoles);
            return res.handler.success(null, users);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeSystemUserStatus(req, res) {
        try {
            const body = req.body;
            let systemPortalRoles = await RoleModal.getSystemPortalRoles();
            systemPortalRoles = systemPortalRoles.map(spr => spr._id);

            await Promise.all([
                SystemPortalModel.updateSystemUserRoleStatus(body.users, systemPortalRoles, body.status, req.headers),
                SystemPortalModel.updateSystemUserStatus(body.users, body.status, req.headers),
            ]);
            let message;
            switch (body.status) {
                case ENTITY_STATUS.ACTIVE:
                    message = "updated_activated_users"
                    break;

                default:
                    message = "updated_in_activated_users"
                    break;
            }

            return res.handler.success(message);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUserDetailsWithRole(req, res) {
        try {
            const userDetails = await SystemPortalModel.getUserById(req.query.userId, { _id: 1, first_name: 1, last_name: 1, email: 1, mobile_number: 1, country_code: 1, profile_pic: 1, is_deleted: 1, is_active: 1 });
            if (!userDetails || userDetails.is_deleted) {
                return res.handler.notFound("validation_not_found_user")
            }
            if (userDetails.profile_pic) {
                userDetails.profile_pic = await fileUpload.getSignedUrl(FILE_PATH.USER_PROFILE, userDetails.profile_pic);
            }
            let systemPortalRoles = await RoleModal.getSystemPortalRoles();
            systemPortalRoles = systemPortalRoles.map(spr => spr._id);

            const userRoleInfo = await SystemPortalModel.systemPortalRoleInfo(
                userDetails._id,
                systemPortalRoles,
                undefined,
                toLeanOption,
            );

            const { profile_pic } = userRoleInfo

            if (profile_pic) {
                userRoleInfo.profile_pic = CommonController.getProfileImageUrl() + profile_pic;
                userRoleInfo.profile_thumbnail_pic = CommonController.getProfileThumbImageUrl() + profile_pic;
            }

            return res.handler.success(null, { userDetails, userRoleInfo })

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addSystemUser(req, res) {
        try {
            // ---------VALIDATORS ----------------- //
            const body = req.body;
            if (req.body.isActive === "true") {
                body.isActive = true;
                req.body.isActive = true;
            } else if (req.body.isActive === "false") {
                body.isActive = false;
                req.body.isActive = false;
            }
            // mobile number and email should not conflict with other user
            let [userWithMobile, userWithEmail, systemPortalRoles, roleInfo] = await Promise.all([
                UserAuthModal.findUserByMobileNumber(body.mobileNumber, body.countryCode),
                UserAuthModal.findUserByEmail(body.email),
                RoleModal.getSystemPortalRoles(),
                RoleModal.getRoleFromRoleID(body.roleId),
            ]);

            if (!roleInfo || roleInfo.name.toLowerCase() === "system owner") {
                return res.handler.badRequest("validation_invalid_role_type");
            }

            let userRole;
            let newUser = false;
            let updateUserDetails = {};
            let userDetails;
            let responseMessage = "added_system_user"
            if (userWithMobile) {

                if (userWithEmail && userWithMobile._id.toString() !== userWithEmail._id.toString()) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                }
                updateUserDetails = {
                    firstName: body.firstName,
                    lastName: body.lastName,
                    previousEmail: userWithMobile.cognito_username,
                    email: body.email,
                    countryCode: body.countryCode,
                    mobileNumber: body.mobileNumber
                };
                if (userWithMobile.is_deleted) {

                    // update user details in cognito
                    // const result = await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                    userWithMobile.is_deleted = false
                    userWithMobile.first_name = body.firstName;
                    userWithMobile.last_name = body.lastName;
                    userWithMobile.email = body.email;
                    userWithMobile.updated_by = req.headers.userDetails._id;
                    userWithMobile.country_code = body.countryCode;
                    userWithMobile.mobile_number = body.mobileNumber;
                    userWithMobile.is_active = body.isActive;
                    [userRole] = await Promise.all([
                        SystemPortalModel.recoverSystemPortalUserProfile(userWithMobile._id, systemPortalRoles.map(r => r._id), req.body, req.headers),
                        // userWithMobile.save()
                    ]);
                    userDetails = userWithMobile;
                    responseMessage = "restored_user";
                    // return res.handler.success("restored_user")
                } else if (!userWithMobile.is_deleted) {
                    userRole = await SystemPortalModel.systemPortalRoleInfo(userWithMobile._id, [roleInfo._id], { role_id: 1, _id: 1, user_id: 1, assigned_tenants: 1, is_active: 1, is_deleted: 1 });
                    if (userRole) {
                        if (!userRole.is_deleted) {
                            return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_mobile_number');
                        } else {
                            userRole.is_active = body.isActive;
                            userRole.is_deleted = false;
                            userRole.collection_name = "users";
                            responseMessage = "restored_user";
                        }
                    } else {
                        const roleName = roleInfo.name.toLowerCase();
                        if (roleName === "account manager") {
                            userRole = await SystemPortalModel.addAccountManagerUserRole(userWithMobile._id, roleInfo._id, req.body.assignedTenants, body.isActive, req.headers);
                        } else if (roleName === "super admin") {
                            userRole = await SystemPortalModel.addSuperAdminUserRole(userWithMobile._id, roleInfo._id._id, body.isActive, req.headers);
                        }
                    }
                    userDetails = userWithMobile;
                }

            } else if (userWithEmail) {
                if (userWithMobile && userWithMobile._id.toString() !== userWithEmail._id.toString()) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                }
                updateUserDetails = {
                    firstName: body.firstName,
                    lastName: body.lastName,
                    previousEmail: userWithEmail.cognito_username,
                    email: body.email,
                    countryCode: body.countryCode,
                    mobileNumber: body.mobileNumber
                }
                if (userWithEmail.is_deleted) {
                    // const result = await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                    userWithEmail.is_deleted = false
                    userWithEmail.first_name = body.firstName;
                    userWithEmail.last_name = body.lastName;
                    userWithEmail.email = body.email;
                    userWithEmail.updated_by = req.headers.userDetails._id;
                    userWithEmail.country_code = body.countryCode;
                    userWithEmail.mobile_number = body.mobileNumber;
                    userWithEmail.is_active = body.isActive;
                    [userRole] = await Promise.all([
                        SystemPortalModel.recoverSystemPortalUserProfile(userWithEmail._id, systemPortalRoles.map(r => r._id), req.body, req.headers),
                        // userWithEmail.save()
                    ]);
                    userDetails = userWithEmail;
                    responseMessage = "restored_user";
                    // return res.handler.success("restored_user")
                } else if (!userWithEmail.is_deleted) {
                    userRole = await SystemPortalModel.systemPortalRoleInfo(userWithEmail._id, [roleInfo._id], { role_id: 1, _id: 1, user_id: 1, assigned_tenants: 1, is_active: 1, is_deleted: 1 });
                    if (userRole) {
                        if (!userRole.is_deleted) {
                            return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_mobile_number');
                        } else {
                            userRole.is_active = body.isActive;
                            userRole.is_deleted = false;
                            responseMessage = "restored_user";
                        }
                    } else {
                        const roleName = roleInfo.name.toLowerCase();
                        if (roleName === "account manager") {
                            userRole = await SystemPortalModel.addAccountManagerUserRole(userWithEmail._id, roleInfo._id, req.body.assignedTenants, body.isActive, req.headers);
                        } else if (roleName === "super admin") {
                            userRole = await SystemPortalModel.addSuperAdminUserRole(userWithEmail._id, roleInfo._id._id, body.isActive, req.headers);
                        }
                    }
                    userDetails = userWithEmail;
                }
                return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
            } else {
                // check role type is SYSTEM_PORTAL ROLE
                newUser = true
                const roleIndex = systemPortalRoles.findIndex(r => r._id.toString() === body.roleId);
                if (roleIndex === -1) {
                    return res.handler.notFound("validation_not_found_role");
                }



                // --------- create user with role in case no user was found with email and mobile number ----------- //
                // add first in aws cognito
                req.body.password = "66G1!d8LviS02x@5i$7"
                const sysPortalUser = await UserAuthModal.signup(req.body);


                const cognitoData = await UserAuthModal.creatUserInUserPool(req.body);
                sysPortalUser.cognito_username = cognitoData.userSub;
                const otp = generateOtp();
                sysPortalUser.forgot_password_otp = otp;
                sysPortalUser.otp_verification_time = new Date();
                sysPortalUser.otp_verification_time_limit = VALUES.PASSWORD_REST_OR_CREATE_DURATION;
                // save user in DB
                await sysPortalUser.save();
                // await UserAuthModal.sendEMailVerificationLink(req.body.email, otp, sysPortalUser._id.toString() ,"Reset Password");
                userDetails = sysPortalUser;
                await UserAuthModal.sendOtpVerificationEMail({ username: `${userDetails.first_name} ${userDetails.last_name}`, otp_num: otp, rtl: false }, undefined, userDetails.email, "Reset Password");
                await Promise.all([UserAuthModal.adminConfirmSignUp(req.body), UserAuthModal.verifyUserDetails(req.body)]);
                // assign user a role provided role id, if role id is or Account manager then assign the assigned Tenants array
                if (systemPortalRoles[roleIndex].name.toLowerCase() === "account manager") {
                    userRole = await SystemPortalModel.addAccountManagerUserRole(sysPortalUser._id, systemPortalRoles[roleIndex]._id, req.body.assignedTenants, body.isActive, req.headers);
                    // await userRole.save();
                } else if (systemPortalRoles[roleIndex].name.toLowerCase() === "super admin") {
                    userRole = await SystemPortalModel.addSuperAdminUserRole(sysPortalUser._id, systemPortalRoles[roleIndex]._id, body.isActive, req.headers)
                    // await userRole.save();
                }

            }

            // upload image to S3
            if (req.body.profilePic) {
                // delete old file if user already exists in the portal
                if (!newUser && userDetails.profile_pic) {
                    await fileUpload.deleteFile(FILE_PATH.USER_PROFILE, userDetails.profile_pic);
                }
                let fileNames = await fileUpload.uploadFiles(FILE_PATH.USER_PROFILE, req.body.profilePic);
                userDetails.profile_pic = fileNames.data[0].fileName;
            }
            await userRole.save();
            if (!newUser) {
                await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                await userDetails.save();
            }
            return res.handler.success(responseMessage);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async editSystemUser(req, res) {
        try {
            // -------- REQUEST VALIDATION ----------- //
            const body = req.body;
            let isSystemOwner = false
            //check user role id
            const userRoleInfo = await UserAuthModal.getUserRoleById(req.body.userRoleId);
            if (!userRoleInfo || userRoleInfo.is_deleted) {
                return res.handler.notFound("validation_not_found_user_role");
            }
            const currentRoleInfo = await RoleModal.getRoleFromRoleID(userRoleInfo.role_id);
            // check mobile number & email conflicts
            let [userWithMobile, userWithEmail, systemPortalRoles, roleInfo, systemOwnerRole] = await Promise.all([
                UserAuthModal.findUserByMobileNumber(body.mobileNumber, body.countryCode),
                UserAuthModal.findUserByEmail(body.email),
                RoleModal.getSystemPortalRoles(),
                RoleModal.getRoleFromRoleID(body.roleId),
                RoleModal.getRoleByFilter({ name: "System Owner", portal_type: VALUES.portals.SYSTEM_PORTAL }, { name: 1 })
            ]);

            if (!roleInfo) {
                return res.handler.notFound("role_not_found");
            }

            if ((userRoleInfo.role_id._id.toString() !== roleInfo.id) && (roleInfo.name.toLowerCase() === "system owner")) {
                return res.handler.badRequest("validation_invalid_role_type");
            }
            if (userRoleInfo.role_id.toString() === systemOwnerRole.id) {
                isSystemOwner = true;
            }


            if (userWithMobile && userWithMobile._id.toString() !== userRoleInfo.user_id.toString()) {
                return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_mobile_number');
            }

            if (userWithEmail && userWithEmail._id.toString() !== userRoleInfo.user_id.toString()) {
                return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
            }

            // check role is on of system portal role
            const roleIndex = systemPortalRoles.findIndex(r => r._id.toString() === body.roleId);
            if (roleIndex === -1) {
                return res.handler.notFound("validation_not_found_role");
            }


            // -------- UPDATE USER OPERATIONS ------- //
            const userDetails = await SystemPortalModel.getUserById(userRoleInfo.user_id);
            // userRole record should match isActive status same for user record too
            userRoleInfo.is_active = body.isActive;
            userRoleInfo.updated_by = req.headers.userDetails._id;
            if (userRoleInfo.role_id.id !== body.roleId) {
                if (userRoleInfo.user_id.toString() === req.headers.userDetails._id.toString()) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, "validation_invalid_role_change");
                }
                // when role has been changed from account manager to super admin, delete assigned tenants
                if (currentRoleInfo.name.toLowerCase() === "account manager" && systemPortalRoles[roleIndex].name.toLowerCase() === "super admin") {
                    userRoleInfo.assigned_tenants = undefined;
                }
                // if role is changed, then request token should be in activated with seSession as has_role_changed is true
                await SystemPortalModel.markSessionAsRoleChanged(body.userRoleId);
                userRoleInfo.role_id = isSystemOwner ? systemOwnerRole._id : body.roleId;
                userRoleInfo.collection_name = "users"
            }
            // TODO: update user details in cognito
            const updateUserDetails = {
                firstName: body.firstName,
                lastName: body.lastName,
                previousEmail: userDetails.email,
                email: body.email,
                countryCode: body.countryCode,
                mobileNumber: body.mobileNumber
            }
            // update user details in cognito
            const promises = []
            if (!body.isActive && userDetails.is_active) {
                promises.push(SystemPortalModel.expireSessionsByUserRoleId(userRoleInfo._id));
            }
            const result = await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
            userDetails.first_name = body.firstName;
            userDetails.last_name = body.lastName;
            userDetails.email = body.email;
            userDetails.country_code = body.countryCode;
            userDetails.mobile_number = body.mobileNumber;
            userDetails.updated_by = req.headers.userDetails._id;
            //TODO: need to check weather need to in-activate the user or not
            userDetails.is_active = body.isActive;
            // save all the changes
            promises.push(userDetails.save(), userRoleInfo.save())

            await Promise.all(promises)
            return res.handler.success("updated_edited");

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteSystemUser(req, res) {
        try {
            const userDetails = await SystemPortalModel.getUserById(req.query.userId);
            if (!userDetails || userDetails.is_deleted) {
                return res.handler.notFound("validation_not_found_user");
            }

            if (userDetails._id.toString() === req.headers.userDetails._id.toString()) {
                return res.handler.badRequest();
            }

            // userDetails.is_deleted = true;
            userDetails.updated_by = req.headers.userDetails._id;
            // TenantPortalModal
            const promises = [];
            // delete all user's assigned roles
            // promises.push(SystemPortalModel.deleteAllUserProfiles(userDetails._id, req.headers));
            // close all the active sessions of this user
            promises.push(SystemPortalModel.closeAllActiveUserSessions(userDetails._id));
            promises.push(userDetails.save());
            promises.push(SystemPortalModel.deleteSystemPortalProfiles(userDetails._id));
            await Promise.all(promises);

            return res.handler.success("deleted_user");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteAssignedTenant(req, res) {
        try {
            req.query.tenantId = parseInt(req.query.tenantId);
            const useRole = await SystemPortalModel.getUserRoleWithRoleInfo(req.query.userRoleId);
            if (!useRole || useRole.is_deleted) {
                return res.handler.notFound("validation_not_found_user_role")
            }

            // if(useRole.role_id.name.toLowerCase() !== "account manager") {
            //     return res.handler.conflict("VALIDATION.INVALID.ACCOUNT_MANAGER_ROLE");
            // }

            await SystemPortalModel.removeAssignedTenant(useRole._id, req.query.tenantId);

            return res.handler.success();

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addAssignedTenant(req, res) {
        try {
            const useRole = await SystemPortalModel.getUserRoleWithRoleInfo(req.body.userRoleId);
            if (!useRole || useRole.is_deleted) {
                return res.handler.notFound("validation_not_found_user_role")
            }

            // if(useRole.role_id.name.toLowerCase() !== "account manager") {
            //     return res.handler.conflict("VALIDATION.INVALID.ACCOUNT_MANAGER_ROLE");
            // }

            await SystemPortalModel.addAssignedTenant(useRole._id, req.body.tenantId);

            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getAssignedTenants(req, res) {
        try {
            const userRole = await SystemPortalModel.getUserRoleWithRoleInfo(req.query.userRoleId);
            if (!userRole || userRole.is_deleted) {
                return res.handler.notFound("validation_not_found_user_role")
            }

            // if(userRole.role_id.name.toLowerCase() !== "account manager") {
            //     return res.handler.conflict("VALIDATION.INVALID.ACCOUNT_MANAGER_ROLE");
            // }
            let result = { list: [], count: 0 };
            if (userRole.assigned_tenants) {
                result = await SystemPortalModel.getAssignedTenantsWithTenantInfo(req.query, userRole.assigned_tenants);
            }

            return res.handler.success(null, result);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async dashboard(req, res) {
        try {
            req.query.tenantId = Number(req.query.tenantId);
            let count = {}

            if (req.query.tenantId) {
                count["tenantUserCount"] = await SystemPortalModel.userCount(req.query.tenantId)
                count["tenantCustomerCount"] = await SystemPortalModel.customerCount(req.query.tenantId)
                count["tenantProductCount"] = await SystemPortalModel.productCount(req)
                return res.handler.success(null, count)
            }

            count["tenantCount"] = await SystemPortalModel.tenantCount()
            count["userCount"] = await SystemPortalModel.userCount()
            count["customerCount"] = await SystemPortalModel.customerCount()
            count["productCount"] = await SystemPortalModel.productCount(req)
            count["ordersCount"] = await SystemPortalModel.orderCount(req)

            return res.handler.success(null, count)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getSalesState(req, res) {
        try {
            const { tenantId, branchId, type, durationPeriod } = req.query;
            let salesAmount;
            if (type === SALES_TYPE.SALES_PERSON) {
                const populationArray = [{
                    path: "user_id",
                    select: {
                        first_name: 1,
                        last_name: 1
                    }
                }]
                const salesPersonsOfBranch = await TenantPortalModal.findSalesPersonWithBranchIdWithPopulate(branchId, null, undefined, { lean: true }, populationArray);
                const body = {
                    tenantId, branchId, type, durationPeriod, salesPerson: salesPersonsOfBranch
                }
                salesAmount = await httpService(req).post("dashboard/salesState", body)
            } else if (type === SALES_TYPE.ALL) {
                const body = {
                    tenantId, branchId, type, durationPeriod
                }
                salesAmount = await httpService(req).post("dashboard/salesState", body)
            }
            return res.handler.success(null, salesAmount.data.data)

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = SystemPortalController
