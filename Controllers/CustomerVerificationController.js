const UserAuthModal = new (require("../Models/auth"))()
const CustomerVerificationModal = new (require("../Models/CustomerVerificationModel"))()

const { VERIFY_CUSTOMER_ACTIONS, BY_PASS_OTP, TENANT_WHATSAPP_TIMEZONES } = require("../Configs/constants")
const TenantPortalModal = new (require("../Models/tenantPortal"))()
const AuthService = new (require("../Services/AuthService"))()
const systemPortalModel = new (require("../Models/systemPortal"))()

const UserModal = new (require("../Models/auth"))();

const { generateOtp } = require("../Utils/helpers")

class CustomerVerificationController {

    async generateNewCustomerOtp(req, res) {
        const { countryCode, mobileNumber } = req.body

        /**
         *  If we have this user's verification info in db then,
         *  remove all the verifications from the db and then
         *  generate new otp.
         */

        const profiles = await UserModal.getTenantInfoWithUserDetails(req.headers.sessionDetails.user_id._id);

        const tenantId = profiles[0].tenant_id._id
        const userTimezone = profiles[0]?.tenant_id?.country?.timezone;
        const isOtpSentOnWhatsapp = TENANT_WHATSAPP_TIMEZONES.includes(userTimezone);

        await CustomerVerificationModal.deleteCustomerVerifications(req.headers.userroleid, mobileNumber)

        const newOtp = generateOtp()
        let userEmail

        let isEmailSent = false
        let isSmsSent = false
        let isWhatsAppSent = false
       
        
        const customerDetails = await TenantPortalModal.findTenantCustomerByMobile(countryCode, mobileNumber);
        const customerData = await systemPortalModel.getCustomerByUserId(customerDetails?._id);
        if (customerData) {
            userEmail = customerData?.customer_email || "";
        }
        logger.info("userEmail", {userEmail})

        if (userEmail) {
            try {
                const response = await UserModal.sendEmailOTP(userEmail, newOtp, "App login Verification OTP")
                logger.info("Email sending response:", response)
                isEmailSent = true
            }
            catch (emailError) {
                logger.error("Email sending failed:", emailError)
            }
            
        }

        if (isOtpSentOnWhatsapp) {
            const configurations = await AuthService.findIntegrationCredentialModel(tenantId);
            if (!configurations) {
                return res.handler.notFound('message_bird_credentials_not_found');
            }
            if (!configurations.is_active) {
                return res.handler.conflict('message_bird_credentials_inactive');
            }
            await AuthService.sendWhatsAppMessage(
                configurations,
                `${countryCode}${mobileNumber}`,
                newOtp
            )
            isWhatsAppSent = true

            if (!isEmailSent && !isWhatsAppSent) {
                if (userEmail) {
                    return res.handler.badRequest("unable_to_send_otp_whatsapp_or_email")
                }
                else {
                    return res.handler.badRequest("unable_to_send_otp_whatsapp")
                }
            }
        }
        else {
            try {
                await UserAuthModal.sendMobileOTP((countryCode + mobileNumber).replace("+", ""), newOtp)
                isSmsSent = true
            }
            catch (smsError) {
                logger.error(smsError)
            }

            if (!isEmailSent && !isSmsSent) {
                if (userEmail) {
                    return res.handler.badRequest("unable_to_send_otp_sms_or_email")
                }
                else {
                    return res.handler.badRequest("unable_to_send_otp_sms")
                }
            }
        }

        const response = await CustomerVerificationModal.addCustomerVerification(req, newOtp)
        await response.save()

        return res.handler.success("process_sms_sent")
    }

    async verifyCustomerOtp(req, res) {
        const { mobileNumber, otp, customerUserRoleId } = req.body
        const customerVerification = await CustomerVerificationModal.findCustomerVerification(req.headers.userroleid, mobileNumber)

        if (!customerVerification?._id) {
            return res.handler.notFound("customer_verification_not_found")
        }

        if ([BY_PASS_OTP, customerVerification.otp].includes(otp)) {
            customerVerification.deleteOne()
            if (customerUserRoleId) {
                const customerVerificationUpdate = await TenantPortalModal.getOnlyUserRoleById(customerUserRoleId)
                customerVerificationUpdate.is_verified = true
                customerVerificationUpdate.save()
            }
            return res.handler.success("customer_verification_success")
        }
        else {
            return res.handler.validationError("validation_device_incorrect_otp")
        }
    }

    verifyCustomer = async (req, res) => {
        try {
            const { action } = req.body

            if (action === VERIFY_CUSTOMER_ACTIONS.SEND_OTP) {
                // Generate OTP to verify customer via mobile number.
                return await this.generateNewCustomerOtp(req, res)
            }
            else {
                // Verify customer via otp.
                return await this.verifyCustomerOtp(req, res)
            }
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }
}

module.exports = CustomerVerificationController
