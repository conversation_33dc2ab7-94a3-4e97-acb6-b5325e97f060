const RewardProgramPointsLogModel = new (require("../../Models/RewardProgram/RewardProgramPointsLogModel"))();
const RewardProgramPointReportModel = new (require("../../Models/RewardProgram/RewardProgramPointReportModel"))();

const RewardProgramController = new (require("./RewardProgramController"))()

const {
    DURATION_PERIOD_OPTIONS,
} = require('../../Configs/constants');

const { logRewardProgram } = require("../../Utils/logHelper");

module.exports = class {

    getDashboardStatistics = async (req, res) => {
        try {
            const tasks = [
                RewardProgramPointReportModel.getStatisticsByYear(req.query),
                RewardProgramPointReportModel.getOpeningBalanceByYear(req.query),
                RewardProgramPointReportModel.getDistributionByYear(req.query),
            ]

            const [
                statistics,
                openingBalance,
                distribution,
            ] = await Promise.all(tasks)

            return res.handler.success(undefined, {
                statistics,
                openingBalance,
                distribution
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getDashboardHistoricDistributions = async (req, res) => {
        try {
            const {
                durationPeriod,
                timezone
            } = req.query

            const {
                reports,
                startDate,
                endDate,
            } = await RewardProgramPointReportModel.getDashboardHistoricDistributions(req.query)

            let date = moment(endDate)

            const data = []

            const units = {
                [DURATION_PERIOD_OPTIONS.DAY]: "days",
                [DURATION_PERIOD_OPTIONS.WEEK]: "week",
                [DURATION_PERIOD_OPTIONS.MONTH]: "months",
            }

            const dateFormats = {
                [DURATION_PERIOD_OPTIONS.DAY]: "DD",
                [DURATION_PERIOD_OPTIONS.WEEK]: "w",
                [DURATION_PERIOD_OPTIONS.MONTH]: "MM",
            }
            const dateFormate = dateFormats[durationPeriod]

            while (date >= startDate) {
                const report = reports.find(report => {
                    return moment.tz(report.date, timezone).format(dateFormate) === date.format(dateFormate)
                })

                data.push({
                    yAxis: date.format("YYYY-MM-DD"),
                    xAxis: {
                        distributed: report?.distributed ?? 0,
                        expired: report?.expired ?? 0,
                        claimed: report?.claimed ?? 0,
                    }
                })
                date = date.subtract(1, units[durationPeriod])
            }

            return res.handler.success(undefined, data)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    addPointsReport = async () => {
        try {
            var profiler = logger.startTimer()
            logger.info("Started: Add reward points reports cron job")

            const addPointsReportByRewardProgram = async (
                rewardProgram,
                sapIntegrationCredentials,
                timezone
            ) => {
                const todayDate = momentTimezone.tz(timezone).subtract(1, "days")
                const startDate = todayDate.clone().startOf("day").format();
                const endDate = todayDate.clone().endOf("day").format();

                const pointLogs = await RewardProgramPointsLogModel.findPointsLogsByFilter({
                    reward_program_id: rewardProgram._id,
                    tenant_id: rewardProgram.tenant_id,
                    created_at: {
                        $gte: startDate,
                        $lte: endDate,
                    },
                })

                if (pointLogs.length) {
                    const lastReport = await RewardProgramPointReportModel.findLastReportByDate({
                        rewardProgramId: rewardProgram._id,
                        tenantId: rewardProgram.tenant_id,
                        lastDate: startDate,
                    })

                    const pointsReport = pointLogs.reduce((report, pointLog) => {
                        const reportInfos = RewardProgramPointReportModel.getReportInfoFormPointsLog(pointLog)
                        reportInfos.forEach(info => {
                            report[info.key] = info.points + (report[info.key] ?? 0)
                        })

                        return report
                    }, {})

                    const lastReportCoinClosingBalance = lastReport?.coins_distribution?.closing_balance ?? 0
                    const distributedCoins = pointsReport["coins_distribution.distributed"] ?? 0
                    const expiredCoins = pointsReport["coins_distribution.expired"] ?? 0
                    const claimedCoins = pointsReport["coins_distribution.claimed"] ?? 0

                    const lastReportVipPointsClosingBalance = lastReport?.vip_points_distribution?.closing_balance ?? 0
                    const distributedVipPoints = pointsReport["vip_points_distribution.distributed"] ?? 0
                    const expiredVipPoints = pointsReport["vip_points_distribution.expired"] ?? 0
                    const claimedVipPoints = pointsReport["vip_points_distribution.claimed"] ?? 0

                    pointsReport["coins_distribution.closing_balance"] = lastReportCoinClosingBalance + distributedCoins - (expiredCoins + claimedCoins)
                    pointsReport["vip_points_distribution.closing_balance"] = lastReportVipPointsClosingBalance + distributedVipPoints - (expiredVipPoints + claimedVipPoints)

                    await RewardProgramPointReportModel.upsertReport(
                        {
                            tenant_id: rewardProgram.tenant_id,
                            reward_program_id: rewardProgram._id,
                            timezone,
                            date: startDate
                        },
                        {
                            "$set": pointsReport,
                        }
                    )
                }
                else {
                    logRewardProgram(`In addPointsReport (${startDate}), no points logs found. rewardProgramId(${rewardProgram._id})`, {
                        tenantId: rewardProgram.tenant_id,
                    })
                }
            }

            await RewardProgramController.rewardProgramsCallBackBasedTenants(addPointsReportByRewardProgram, true)
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "ADD REWARD POINTS REPORTS JOB RUN TIME" })
            logger.info(`Completed: Add reward points reports cron job\n`)
        }
    }

}
