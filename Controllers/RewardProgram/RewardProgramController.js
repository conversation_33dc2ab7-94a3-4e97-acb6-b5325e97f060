const RewardProgramModel = new (require("../../Models/RewardProgram/RewardProgramModel"))();
const RewardProgramConfigurationModel = new (require("../../Models/RewardProgram/RewardProgramConfigurationModel"))();
const RewardProgramMemberModel = new (require("../../Models/RewardProgram/RewardProgramMemberModel"))();

const IntegrationCredentialModel = new (require('../../Models/IntegrationCredentialModel'))();
const TenantPortalModal = new (require("../../Models/tenantPortal"))();

const {
    INTEGRATION_CHANNELS,
    ENTITY_STATUS
} = require('../../Configs/constants');

const {
    convertToSnakeCase
} = require("../../Utils/helpers");

const { logRewardProgram } = require("../../Utils/logHelper");

module.exports = class {

    addRewardProgram = async (req, res) => {
        try {
            await RewardProgramModel.addRewardProgram(
                convertToSnakeCase(req.body),
                req.headers.userDetails._id,
            )

            return res.handler.success("reward_program_added")
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    updateRewardProgram = async (req, res) => {
        try {
            const {
                tenantId,
                rewardProgramId,
                isActive,
            } = req.body

            const rewardProgram = await RewardProgramModel.findRewardProgramById(rewardProgramId)
            if (!rewardProgram)
                return res.handler.notFound("reward_program_not_found")

            if (
                rewardProgram.is_active &&
                !isActive
            ) {
                const activeMember = await RewardProgramMemberModel.findMemberByFilter(
                    {
                        tenantId,
                        rewardProgramId,
                        status: ENTITY_STATUS.ACTIVE
                    },
                    "_id"
                )

                if (activeMember)
                    return res.handler.conflict("cant_deactivate_reward_program_members_exist")
            }

            convertToSnakeCase(req.body, rewardProgram)

            await RewardProgramModel.saveRewardProgram(
                rewardProgram,
                req.headers.userDetails._id,
            )

            return res.handler.success("reward_program_updated")
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    listRewardProgram = async (req, res) => {
        try {
            const data = await RewardProgramModel.findRewardProgramWithPagination(req.query)
            return res.handler.success(undefined, data)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    updateRewardProgramConfiguration = async (req, res) => {
        try {
            const configuration = await RewardProgramConfigurationModel.findOrCreateConfiguration(
                req.body.tenantId,
                req.headers.userDetails._id,
                false
            )
            convertToSnakeCase(req.body, configuration)

            await RewardProgramConfigurationModel.save(
                configuration,
                req.headers.userDetails._id,
            )

            return res.handler.success("reward_program_configuration_updated")
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getRewardProgramConfiguration = async (req, res) => {
        try {
            const configuration = await RewardProgramConfigurationModel.findOrCreateConfiguration(
                req.query.tenantId,
                req.headers.userDetails._id,
            )

            return res.handler.success(undefined, configuration)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    rewardProgramsCallBackBasedTenants = async (
        callBack,
        withTenantTimeZone = false,
    ) => {
        const tenantCallback = async (
            tenantId,
            sapIntegrationCredentials
        ) => {
            const rewardPrograms = await RewardProgramModel.findRewardProgram({
                tenantId,
                isActive: true
            })

            logRewardProgram(`Found ${rewardPrograms.length} rewardPrograms`, {
                tenantId,
                rewardPrograms,
            })

            let timezone

            if (withTenantTimeZone) {
                const tenant = await TenantPortalModal.getTenantById(
                    tenantId,
                    "_id",
                    {
                        populate: [
                            {
                                path: "country",
                                select: "-_id timezone"
                            }
                        ],
                        lean: true
                    }
                )

                timezone = tenant.country.timezone
            }

            for (let i = 0; i < rewardPrograms.length; i++) {
                try {
                    await callBack(
                        rewardPrograms[i],
                        sapIntegrationCredentials,
                        timezone
                    )
                }
                catch (error) {
                    logger.error(error)
                }
            }
        }

        const credentials = await IntegrationCredentialModel.getCredentials(
            {
                name: INTEGRATION_CHANNELS.SAP_SERVICE,
                is_active: true,
                "configurations.is_reward_program_enabled": true
            },
            "tenant_id configurations",
            {
                lean: true
            }
        );

        for (let i = 0; i < credentials.length; i++) {
            try {
                await tenantCallback(credentials[i].tenant_id, credentials[i])
            }
            catch (error) {
                logger.error(error)
            }
        }
    }

}
